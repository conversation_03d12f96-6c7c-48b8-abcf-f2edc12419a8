import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get_it/get_it.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';
import 'package:market/features/suppliers/domain/usecases/create_supplier.dart';
import 'package:market/features/suppliers/domain/usecases/update_supplier.dart';
import 'package:market/features/suppliers/domain/usecases/get_all_suppliers.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_by_id.dart';
import 'package:market/features/suppliers/domain/usecases/delete_supplier.dart';
import 'package:market/features/suppliers/domain/usecases/add_supplier_account_entry.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_account_statement.dart';
import 'package:market/features/purchases/domain/repositories/purchase_repository.dart';
import 'package:market/utils/excel_importer.dart';
import '../../../suppliers/domain/entities/supplier_account.dart';

class SupplierProvider extends ChangeNotifier {
  final CreateSupplier _createSupplierUseCase;
  final UpdateSupplier _updateSupplierUseCase;
  final GetAllSuppliers _getAllSuppliersUseCase;
  final GetSupplierById _getSupplierByIdUseCase;
  final DeleteSupplier _deleteSupplierUseCase;
  final AddSupplierAccountEntryUseCase _addSupplierAccountEntryUseCase;
  final GetSupplierAccountStatementUseCase _getSupplierAccountStatementUseCase;

  SupplierProvider(
    this._createSupplierUseCase,
    this._updateSupplierUseCase,
    this._getAllSuppliersUseCase,
    this._getSupplierByIdUseCase,
    this._deleteSupplierUseCase,
    this._addSupplierAccountEntryUseCase,
    this._getSupplierAccountStatementUseCase,
  );

  List<Supplier> _suppliers = [];
  bool _isLoading = false;
  String? _errorMessage;

  // +++ متغيرات جديدة لكشف الحساب +++
  List<SupplierAccountStatementItem> _statementItems = [];

  List<Supplier> get suppliers => _suppliers;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<SupplierAccountStatementItem> get statementItems => _statementItems;

  /// Load all suppliers
  Future<void> loadSuppliers() async {
    _setLoading(true);
    _clearError();

    try {
      _suppliers = await _getAllSuppliersUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الموردين: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Import suppliers from device contacts
  Future<int> importFromContacts() async {
    _setLoading(true);
    _clearError();

    try {
      // Request permission to access contacts
      if (!await FlutterContacts.requestPermission()) {
        _setError(
          'تم رفض إذن الوصول إلى جهات الاتصال. يرجى تمكينه يدوياً من الإعدادات.',
        );
        await openAppSettings();
        throw Exception('تم رفض إذن الوصول.');
      }

      // Get all contacts with phone numbers
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      int importedCount = 0;

      for (final contact in contacts) {
        if (contact.displayName.isNotEmpty) {
          try {
            final supplier = Supplier(
              name: contact.displayName.trim(),
              phone: contact.phones.isNotEmpty
                  ? contact.phones.first.number.trim()
                  : null,
              email: contact.emails.isNotEmpty
                  ? contact.emails.first.address.trim()
                  : null,
              address: contact.addresses.isNotEmpty
                  ? contact.addresses.first.address.trim()
                  : null,
              contactPerson: null,
              notes: null,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _createSupplierUseCase.call(supplier);
            importedCount++;
          } catch (e) {
            // Skip contacts that fail to import
            continue;
          }
        }
      }

      // Refresh the list after import
      await loadSuppliers();

      return importedCount;
    } catch (e) {
      _setError('فشل في استيراد الموردين من جهات الاتصال: ${e.toString()}');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Import suppliers from Excel file
  Future<ImportResult> importSuppliers(File file) async {
    _setLoading(true);
    _clearError();

    try {
      final importedSuppliers = await ExcelImporter.importSuppliers(file);

      int successCount = 0;
      int errorCount = 0;
      List<String> errors = [];

      for (final supplier in importedSuppliers) {
        try {
          // Use the real use case to save suppliers to database
          await _createSupplierUseCase.call(supplier);
          successCount++;
        } catch (e) {
          errorCount++;
          errors.add('خطأ في المورد ${supplier.name}: $e');
        }
      }

      // Refresh the list after import
      await loadSuppliers();

      notifyListeners();

      return ImportResult(
        totalCount: importedSuppliers.length,
        successCount: successCount,
        errorCount: errorCount,
        errors: errors,
      );
    } catch (e) {
      _setError('فشل في استيراد الموردين: ${e.toString()}');
      return ImportResult(
        totalCount: 0,
        successCount: 0,
        errorCount: 1,
        errors: [e.toString()],
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new supplier
  Future<bool> addSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      await _createSupplierUseCase.call(supplier);
      await loadSuppliers(); // Refresh the list
      return true;
    } catch (e) {
      _setError('فشل في إضافة المورد: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing supplier
  Future<bool> updateSupplier(Supplier supplier) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateSupplierUseCase.call(supplier);
      await loadSuppliers(); // Refresh the list
      return true;
    } catch (e) {
      _setError('فشل في تحديث المورد: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a supplier
  Future<bool> deleteSupplier(int supplierId) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteSupplierUseCase.call(supplierId);
      await loadSuppliers(); // Refresh the list
      return true;
    } catch (e) {
      _setError('فشل في حذف المورد: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Search suppliers by name or phone
  List<Supplier> searchSuppliers(String query) {
    if (query.isEmpty) return _suppliers;

    return _suppliers.where((supplier) {
      final name = supplier.name.toLowerCase();
      final phone = supplier.phone?.toLowerCase() ?? '';
      final email = supplier.email?.toLowerCase() ?? '';
      final contactPerson = supplier.contactPerson?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) ||
          phone.contains(searchQuery) ||
          email.contains(searchQuery) ||
          contactPerson.contains(searchQuery);
    }).toList();
  }

  // +++ دوال جديدة لإدارة حساب المورد +++

  /// هذه الدالة سيتم استدعاؤها من PurchaseProvider و PaymentReceiptProvider
  Future<void> addSupplierAccountEntry(SupplierAccount entry) async {
    try {
      await _addSupplierAccountEntryUseCase.call(entry);

      // +++ الإصلاح الحاسم +++
      // يجب إعادة تحميل الموردين لتحديث أرصدتهم وإعلام المستمعين
      await loadSuppliers(); // هذا سيقوم بـ notifyListeners() داخلياً
      // --- نهاية الإصلاح ---
    } catch (e) {
      _setError('فشل في إضافة قيد حساب المورد: ${e.toString()}');
      notifyListeners(); // إعلام المستمعين بالخطأ فقط
    }
  }

  /// هذه الدالة ستُستخدم في شاشة كشف حساب المورد
  Future<void> fetchSupplierAccountStatement(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    _setLoading(true);
    _clearError();
    print(
      '🔍 DEBUG: Starting fetchSupplierAccountStatement for supplierId: $supplierId, fromDate: $fromDate, toDate: $toDate',
    );

    try {
      print(
        '🔍 DEBUG: About to call _getSupplierAccountStatementUseCase.call()',
      );
      _statementItems = await _getSupplierAccountStatementUseCase.call(
        supplierId,
        fromDate: fromDate,
        toDate: toDate,
      );
      print('📊 DEBUG: Fetched ${_statementItems.length} statement items.');
      print(
        '📊 DEBUG: _getSupplierAccountStatementUseCase.call() completed successfully',
      );
    } catch (e) {
      _setError('فشل تحميل كشف الحساب: ${e.toString()}');
      print('❌ ERROR: Failed to fetch supplier statement: $e');
    } finally {
      _setLoading(false);
      print(
        '✅ DEBUG: fetchSupplierAccountStatement completed for supplierId: $supplierId',
      );
    }
  }

  /// Get supplier account statement (simplified method)
  Future<List<SupplierAccountStatementItem>> getSupplierAccountStatement(
    int supplierId,
  ) async {
    return await _getSupplierAccountStatementUseCase.call(supplierId);
  }

  /// Get supplier financial summary
  Future<Map<String, dynamic>> getSupplierFinancialSummary(
    int supplierId,
  ) async {
    try {
      final purchaseRepository = GetIt.instance<PurchaseRepository>();

      final totalPurchases = await purchaseRepository
          .getTotalPurchasesBySupplier(supplierId);
      final totalPayments = await purchaseRepository
          .getTotalPaymentsMadeToSupplier(supplierId);

      return {'totalPurchases': totalPurchases, 'totalPayments': totalPayments};
    } catch (e) {
      _setError('Failed to get supplier financial summary: ${e.toString()}');
      return {'totalPurchases': 0.0, 'totalPayments': 0.0};
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearSuppliers() {
    _suppliers.clear();
    notifyListeners();
  }

  // CRUD operations for SupplierFormScreen
  Future<List<Supplier>> getAllSuppliers() async {
    try {
      return await _getAllSuppliersUseCase.call();
    } catch (e) {
      _setError('فشل في جلب الموردين: ${e.toString()}');
      return [];
    }
  }

  Future<Supplier?> getSupplierById(int id) async {
    try {
      return await _getSupplierByIdUseCase.call(id);
    } catch (e) {
      _setError('فشل في جلب المورد: ${e.toString()}');
      return null;
    }
  }

  Future<void> addSupplierEntity(Supplier supplier) async {
    try {
      await _createSupplierUseCase.call(supplier);
      await loadSuppliers(); // Refresh the list
    } catch (e) {
      _setError('فشل في إضافة المورد: ${e.toString()}');
      rethrow;
    }
  }

  Future<void> updateSupplierEntity(Supplier supplier) async {
    try {
      await _updateSupplierUseCase.call(supplier);
      await loadSuppliers(); // Refresh the list
    } catch (e) {
      _setError('فشل في تحديث المورد: ${e.toString()}');
      rethrow;
    }
  }
}

class ImportResult {
  final int totalCount;
  final int successCount;
  final int errorCount;
  final List<String> errors;

  ImportResult({
    required this.totalCount,
    required this.successCount,
    required this.errorCount,
    required this.errors,
  });

  bool get hasErrors => errorCount > 0;
  bool get isSuccess => successCount > 0;

  String get summary {
    if (totalCount == 0) return 'لم يتم العثور على بيانات للاستيراد';
    if (errorCount == 0) {
      return 'تم استيراد جميع البيانات بنجاح ($successCount عنصر)';
    }
    if (successCount == 0) {
      return 'فشل في استيراد جميع البيانات ($errorCount خطأ)';
    }
    return 'تم استيراد $successCount من أصل $totalCount عنصر ($errorCount خطأ)';
  }
}
