import '../entities/sale.dart';
import '../repositories/sale_repository.dart';

class GetSaleByIdUseCase {
  final SaleRepository _repository;

  GetSaleByIdUseCase(this._repository);

  Future<Sale?> call(int id) async {
    if (id <= 0) {
      throw Exception('Sale ID must be greater than 0');
    }

    try {
      return await _repository.getSaleById(id);
    } catch (e) {
      throw Exception('Failed to get sale by id: $e');
    }
  }
}
