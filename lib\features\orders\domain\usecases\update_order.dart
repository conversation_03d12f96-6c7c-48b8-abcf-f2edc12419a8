import '../entities/order.dart';
import '../repositories/order_repository.dart';

class UpdateOrderUseCase {
  final OrderRepository _repository;

  UpdateOrderUseCase(this._repository);

  Future<void> call(Order order) async {
    // Validation
    if (order.id == null || order.id! <= 0) {
      throw Exception('Order ID must be provided and greater than 0');
    }

    if (order.totalEstimatedCost < 0) {
      throw Exception('Total estimated cost cannot be negative');
    }

    if (order.status.isEmpty) {
      throw Exception('Order status cannot be empty');
    }

    try {
      await _repository.updateOrder(order);
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }
}
