import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/supplier_account_model.dart';

class SupplierAccountDatabaseService {
  final DatabaseService _databaseService;

  SupplierAccountDatabaseService(this._databaseService);

  /// Add a new supplier account entry
  Future<int> addSupplierAccountEntry(SupplierAccountModel entry) async {
    final db = await _databaseService.database;
    final entryMap = entry.toMap();
    entryMap.remove('id'); // Remove id for auto-increment

    print('🔍 DEBUG: Attempting to insert supplier_account entry: $entryMap');
    try {
      final id = await db.insert(
        'supplier_accounts',
        entryMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      print(
        '✅ DEBUG: Successfully inserted supplier_account entry with id: $id',
      );
      return id;
    } catch (e) {
      print('❌ ERROR: Failed to insert supplier_account entry: $e');
      rethrow; // أعد إلقاء الخطأ ليتم التقاطه في الطبقات العليا
    }
  }

  /// Get supplier account statement
  Future<List<SupplierAccountModel>> getSupplierAccountStatement(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseService.database;

    String whereClause = 'supplierId = ?';
    List<dynamic> whereArgs = [supplierId];

    if (fromDate != null) {
      whereClause += ' AND transactionDate >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      whereClause += ' AND transactionDate <= ?';
      whereArgs.add(toDate.toIso8601String());
    }

    print(
      '🔍 DEBUG: Fetching supplier_accounts for supplierId: $supplierId, fromDate: $fromDate, toDate: $toDate',
    );
    print('🔍 DEBUG: SQL WHERE clause: $whereClause, args: $whereArgs');

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'supplier_accounts',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transactionDate DESC, id DESC',
      );

      print(
        '📊 DEBUG: SupplierAccountDatabaseService fetched ${maps.length} raw maps for supplierId: $supplierId',
      );

      for (var map in maps) {
        print('📋 DEBUG: Supplier account raw map: $map');
      }

      return List.generate(maps.length, (i) {
        return SupplierAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      print('❌ ERROR: Failed to get supplier account statement: $e');
      rethrow;
    }
  }

  /// Get all supplier account entries
  Future<List<SupplierAccountModel>> getAllSupplierAccountEntries() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'supplier_accounts',
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return SupplierAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get all supplier account entries: $e');
    }
  }

  /// Delete supplier account entry
  Future<void> deleteSupplierAccountEntry(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete('supplier_accounts', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('Failed to delete supplier account entry: $e');
    }
  }

  /// Delete all entries for a supplier
  Future<void> deleteAllEntriesForSupplier(int supplierId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'supplier_accounts',
        where: 'supplierId = ?',
        whereArgs: [supplierId],
      );
    } catch (e) {
      throw Exception('Failed to delete all entries for supplier: $e');
    }
  }

  /// Get supplier account entries (alias for getSupplierAccountStatement)
  Future<List<SupplierAccountModel>> getSupplierAccountEntries(
    int supplierId,
  ) async {
    return await getSupplierAccountStatement(supplierId);
  }

  /// Delete supplier account entries (alias for deleteAllEntriesForSupplier)
  Future<void> deleteSupplierAccountEntries(int supplierId) async {
    return await deleteAllEntriesForSupplier(supplierId);
  }

  /// Calculate supplier account balance
  Future<double> getSupplierAccountBalance(int supplierId) async {
    try {
      final db = await _databaseService.database;

      // Calculate balance: purchase_invoice amounts (debits) - payment_out amounts (credits)
      final result = await db.rawQuery(
        '''
        SELECT
          SUM(CASE WHEN type = 'purchase_invoice' THEN amount ELSE 0 END) as totalDebits,
          SUM(CASE WHEN type = 'payment_out' THEN amount ELSE 0 END) as totalCredits
        FROM supplier_accounts
        WHERE supplierId = ?
      ''',
        [supplierId],
      );

      if (result.isNotEmpty) {
        final totalDebits =
            (result.first['totalDebits'] as num?)?.toDouble() ?? 0.0;
        final totalCredits =
            (result.first['totalCredits'] as num?)?.toDouble() ?? 0.0;
        return totalDebits -
            totalCredits; // Balance = what we owe - what we paid
      }

      return 0.0;
    } catch (e) {
      throw Exception('Failed to get supplier account balance: $e');
    }
  }
}
