import '../../data/models/activity_model.dart';

class Activity {
  final int? id;
  final String type;
  final DateTime date;
  final String description;
  final int? relatedId;
  final double? amount;
  final bool canEdit;
  final String? targetRoute;
  final bool isModified;
  final String? paymentMethod; // إضافة هذا الحقل

  const Activity({
    this.id,
    required this.type,
    required this.date,
    required this.description,
    this.relatedId,
    this.amount,
    this.canEdit = true,
    this.targetRoute,
    this.isModified = false,
    this.paymentMethod, // إضافة هذا الحقل
  });

  // Create Activity from ActivityModel
  factory Activity.fromModel(ActivityModel model) {
    return Activity(
      id: model.id,
      type: model.type,
      date: model.date,
      description: model.description,
      relatedId: model.relatedId,
      amount: model.amount,
      canEdit: model.canEdit,
      targetRoute: model.targetRoute,
      isModified: model.isModified,
      paymentMethod: model.paymentMethod, // قراءة الحقل
    );
  }

  // Business logic methods
  String get typeDisplayName {
    switch (type) {
      case 'sale':
        return 'بيع';
      case 'purchase':
        return 'شراء';
      case 'expense':
        return 'مصروف';
      case 'transfer':
        return 'تحويل داخلي';
      case 'payment':
        return 'دفع';
      case 'payment_receipt': // إضافة هذا النوع الجديد
        return 'سند مالي';
      case 'retail_debt':
        return 'دين تجزئة';
      case 'inventory_count':
        return 'جرد مخزون';
      case 'order':
        return 'طلبية';
      case 'notification':
        return 'تنبيه';
      default:
        return 'نشاط غير معرف'; // تأكد أن default دائماً معرب
    }
  }

  String get statusText {
    return isModified ? 'تم التعديل' : 'لم يتم التعديل';
  }

  String get formattedAmount {
    if (amount == null) return '';
    return '${amount!.toStringAsFixed(2)} ر.ي';
  }

  bool get hasAmount => amount != null && amount! > 0;

  // Copy with method for updates
  Activity copyWith({
    int? id,
    String? type,
    DateTime? date,
    String? description,
    int? relatedId,
    double? amount,
    bool? canEdit,
    String? targetRoute,
    bool? isModified,
    String? paymentMethod, // إضافة الحقل لـ copyWith
  }) {
    return Activity(
      id: id ?? this.id,
      type: type ?? this.type,
      date: date ?? this.date,
      description: description ?? this.description,
      relatedId: relatedId ?? this.relatedId,
      amount: amount ?? this.amount,
      canEdit: canEdit ?? this.canEdit,
      targetRoute: targetRoute ?? this.targetRoute,
      isModified: isModified ?? this.isModified,
      paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }

  @override
  String toString() {
    return 'Activity(id: $id, type: $type, date: $date, '
        'description: $description, relatedId: $relatedId, amount: $amount, '
        'canEdit: $canEdit, targetRoute: $targetRoute, isModified: $isModified)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Activity &&
        other.id == id &&
        other.type == type &&
        other.date == date &&
        other.description == description &&
        other.relatedId == relatedId &&
        other.amount == amount &&
        other.canEdit == canEdit &&
        other.targetRoute == targetRoute &&
        other.isModified == isModified;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        date.hashCode ^
        description.hashCode ^
        relatedId.hashCode ^
        amount.hashCode ^
        canEdit.hashCode ^
        targetRoute.hashCode ^
        isModified.hashCode;
  }
}
