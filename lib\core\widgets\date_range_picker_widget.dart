import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateRangePickerWidget extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final Function(DateTime? startDate, DateTime? endDate) onDateRangeChanged;
  final String title;

  const DateRangePickerWidget({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    required this.onDateRangeChanged,
    this.title = 'تحديد المدة الزمنية',
  });

  @override
  State<DateRangePickerWidget> createState() => _DateRangePickerWidgetState();
}

class _DateRangePickerWidgetState extends State<DateRangePickerWidget> {
  DateTime? _startDate;
  DateTime? _endDate;
  final DateFormat _dateFormat = DateFormat('yyyy/MM/dd');

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        // إذا كان تاريخ البداية بعد تاريخ النهاية، قم بإعادة تعيين تاريخ النهاية
        if (_endDate != null && _startDate!.isAfter(_endDate!)) {
          _endDate = null;
        }
      });
      widget.onDateRangeChanged(_startDate, _endDate);
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
      widget.onDateRangeChanged(_startDate, _endDate);
    }
  }

  void _clearDates() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    widget.onDateRangeChanged(null, null);
  }

  void _setQuickRange(int days) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));
    
    setState(() {
      _startDate = startDate;
      _endDate = endDate;
    });
    widget.onDateRangeChanged(_startDate, _endDate);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // أزرار الاختيار السريع
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickButton('اليوم', 0),
                _buildQuickButton('أسبوع', 7),
                _buildQuickButton('شهر', 30),
                _buildQuickButton('3 أشهر', 90),
                _buildQuickButton('سنة', 365),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // اختيار التواريخ
            Row(
              children: [
                Expanded(
                  child: _buildDateButton(
                    label: 'من تاريخ',
                    date: _startDate,
                    onTap: _selectStartDate,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateButton(
                    label: 'إلى تاريخ',
                    date: _endDate,
                    onTap: _selectEndDate,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // زر المسح
            Center(
              child: TextButton.icon(
                onPressed: _clearDates,
                icon: const Icon(Icons.clear),
                label: const Text('مسح التواريخ'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickButton(String label, int days) {
    final isSelected = _startDate != null && _endDate != null &&
        _startDate!.difference(DateTime.now()).inDays.abs() <= days + 1 &&
        _endDate!.difference(DateTime.now()).inDays.abs() <= 1;

    return ElevatedButton(
      onPressed: () => _setQuickRange(days),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Theme.of(context).primaryColor : null,
        foregroundColor: isSelected ? Colors.white : null,
      ),
      child: Text(label),
    );
  }

  Widget _buildDateButton({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 4),
            Text(
              date != null ? _dateFormat.format(date) : 'اختر التاريخ',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: date != null ? null : Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
