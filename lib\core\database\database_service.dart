import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._();
  static Database? _database;

  DatabaseService._();

  factory DatabaseService() => _instance;

  static DatabaseService get instance => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Set test database for unit testing
  Future<void> setTestDatabase(Database testDb) async {
    _database = testDb;
  }

  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, 'market.db');

      return await openDatabase(
        path,
        version: 17, // *** إعادة الإصدار إلى 17 لإزالة التقارير المتقدمة ***
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      // For now, return an in-memory database as fallback
      return await openDatabase(':memory:', version: 10, onCreate: _onCreate);
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create categories table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE
      )
    ''');

    // Create products table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        unit TEXT NOT NULL,
        lastPurchasePrice REAL,
        wholesalePrice REAL NOT NULL DEFAULT 0,
        retailPrice REAL NOT NULL DEFAULT 0,
        minStockQuantity INTEGER NOT NULL DEFAULT 0,
        barcode TEXT UNIQUE,
        warehouseQuantity INTEGER NOT NULL DEFAULT 0,
        storeQuantity INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create app_info table for version tracking
    await db.execute('''
      CREATE TABLE IF NOT EXISTS app_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // Create purchase_batches table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS purchase_batches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        purchaseDate TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unitPurchasePrice REAL NOT NULL,
        remainingQuantity INTEGER NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create internal_transfers table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS internal_transfers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        transferDate TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        retailPriceAtTransfer REAL NOT NULL,
        costAtTransfer REAL NOT NULL,
        totalValue REAL NOT NULL,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create orders table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderDate TEXT NOT NULL,
        totalEstimatedCost REAL NOT NULL,
        notes TEXT,
        status TEXT NOT NULL
      )
    ''');

    // Create order_items table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId INTEGER NOT NULL,
        productId INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        estimatedUnitPrice REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create app_notifications table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS app_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        message TEXT NOT NULL,
        date TEXT NOT NULL,
        relatedEntityId INTEGER,
        relatedEntityType TEXT,
        suggestedAction TEXT,
        suggestedActionRoute TEXT,
        isRead INTEGER NOT NULL DEFAULT 0,
        isActionTaken INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create sales table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customerId INTEGER,
        saleDate TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        discountAmount REAL NOT NULL DEFAULT 0.0,
        totalPaidAmount REAL NOT NULL DEFAULT 0,
        cost_of_goods_sold REAL NOT NULL DEFAULT 0.0,
        profit REAL NOT NULL DEFAULT 0.0,
        paymentMethod TEXT NOT NULL,
        notes TEXT,
        status TEXT NOT NULL,
        FOREIGN KEY (customerId) REFERENCES customers(id) ON DELETE SET NULL
      )
    ''');

    // Create sale_items table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sale_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        saleId INTEGER NOT NULL,
        productId INTEGER,
        quantity INTEGER,
        unitPrice REAL NOT NULL,
        itemType TEXT NOT NULL,
        description TEXT,
        FOREIGN KEY (saleId) REFERENCES sales(id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create purchases table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS purchases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplierId INTEGER,
        purchaseDate TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        totalPaidAmount REAL NOT NULL DEFAULT 0,
        paymentMethod TEXT NOT NULL,
        notes TEXT,
        status TEXT NOT NULL,
        FOREIGN KEY (supplierId) REFERENCES suppliers(id) ON DELETE SET NULL
      )
    ''');

    // Create purchase_items table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS purchase_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        purchaseId INTEGER NOT NULL,
        productId INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        FOREIGN KEY (purchaseId) REFERENCES purchases(id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create customer_accounts table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS customer_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customerId INTEGER NOT NULL,
        transactionDate TEXT NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        relatedInvoiceId INTEGER,
        isPaid INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (customerId) REFERENCES customers(id) ON DELETE CASCADE
      )
    ''');

    // Create inventory_counts table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS inventory_counts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        countDate TEXT NOT NULL,
        countedWarehouseQuantity INTEGER NOT NULL,
        countedStoreQuantity INTEGER NOT NULL,
        systemWarehouseQuantity INTEGER NOT NULL,
        systemStoreQuantity INTEGER NOT NULL,
        warehouseDifference INTEGER NOT NULL,
        storeDifference INTEGER NOT NULL,
        notes TEXT,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
      )
    ''');

    // Create customers table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        credit_limit REAL NOT NULL DEFAULT 0.0,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create suppliers table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        contact_person TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create expenses table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        expense_date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create customer_account_entries table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS customer_account_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      )
    ''');

    // Create payment_receipts table (سندات القبض/الدفع)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS payment_receipts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        relatedEntityId INTEGER,
        relatedEntityType TEXT NOT NULL,
        transactionDate TEXT NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        paymentMethod TEXT NOT NULL,
        description TEXT,
        relatedInvoiceId INTEGER,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // +++ إضافة جدول حسابات الموردين +++
    await db.execute('''
      CREATE TABLE IF NOT EXISTS supplier_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplierId INTEGER NOT NULL,
        transactionDate TEXT NOT NULL,
        type TEXT NOT NULL, -- 'purchase_invoice', 'payment_out'
        amount REAL NOT NULL,
        description TEXT,
        relatedInvoiceId INTEGER,
        FOREIGN KEY (supplierId) REFERENCES suppliers(id) ON DELETE CASCADE
      )
    ''');
    // --- نهاية الإضافة ---

    // +++ إضافة جداول التقارير المتقدمة +++

    // جدول تخزين مؤقت للتحليلات (لتحسين الأداء)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS analytics_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cache_key TEXT NOT NULL UNIQUE,
        cache_data TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        expires_at TEXT NOT NULL
      )
    ''');

    // جدول إعدادات التقارير
    await db.execute('''
      CREATE TABLE IF NOT EXISTS report_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول سجل التقارير المُصدرة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS exported_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_type TEXT NOT NULL,
        report_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        exported_at TEXT NOT NULL,
        file_size INTEGER,
        export_format TEXT NOT NULL
      )
    ''');

    // جدول تتبع أداء المنتجات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS product_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        date TEXT NOT NULL,
        sales_quantity INTEGER NOT NULL DEFAULT 0,
        sales_revenue REAL NOT NULL DEFAULT 0,
        purchase_quantity INTEGER NOT NULL DEFAULT 0,
        purchase_cost REAL NOT NULL DEFAULT 0,
        profit REAL NOT NULL DEFAULT 0,
        profit_margin REAL NOT NULL DEFAULT 0,
        FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE(productId, date)
      )
    ''');

    // جدول تتبع أداء العملاء
    await db.execute('''
      CREATE TABLE IF NOT EXISTS customer_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customerId INTEGER NOT NULL,
        date TEXT NOT NULL,
        total_purchases REAL NOT NULL DEFAULT 0,
        order_count INTEGER NOT NULL DEFAULT 0,
        average_order_value REAL NOT NULL DEFAULT 0,
        last_purchase_date TEXT,
        FOREIGN KEY (customerId) REFERENCES customers(id) ON DELETE CASCADE,
        UNIQUE(customerId, date)
      )
    ''');

    // جدول تتبع أداء الموردين
    await db.execute('''
      CREATE TABLE IF NOT EXISTS supplier_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplierId INTEGER NOT NULL,
        date TEXT NOT NULL,
        total_purchases REAL NOT NULL DEFAULT 0,
        order_count INTEGER NOT NULL DEFAULT 0,
        average_order_value REAL NOT NULL DEFAULT 0,
        last_purchase_date TEXT,
        FOREIGN KEY (supplierId) REFERENCES suppliers(id) ON DELETE CASCADE,
        UNIQUE(supplierId, date)
      )
    ''');
    // --- نهاية إضافة جداول التقارير ---

    // Insert initial data
    await db.insert('app_info', {
      'version': '1.0.0',
      'created_at': DateTime.now().toIso8601String(),
    });

    // Insert default categories
    await _insertDefaultCategories(db);

    // Create indexes for better performance
    await _createIndexes(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Create categories table if it doesn't exist
      await db.execute('''
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE
        )
      ''');

      // Create products table if it doesn't exist
      await db.execute('''
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL,
          unit TEXT NOT NULL,
          purchasePrice REAL NOT NULL DEFAULT 0,
          salePrice REAL NOT NULL DEFAULT 0,
          minStockQuantity INTEGER NOT NULL DEFAULT 0,
          barcode TEXT UNIQUE,
          warehouseQuantity INTEGER NOT NULL DEFAULT 0,
          storeQuantity INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Insert default categories if table is empty
      final count =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM categories'),
          ) ??
          0;

      if (count == 0) {
        await _insertDefaultCategories(db);
      }
    }

    if (oldVersion < 3) {
      // Update products table structure for new pricing model
      // Drop the old table and recreate with new structure
      // (This will delete existing product data, but it's acceptable for development)
      await db.execute('DROP TABLE IF EXISTS products');
      await db.execute('''
        CREATE TABLE products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL,
          unit TEXT NOT NULL,
          lastPurchasePrice REAL,
          wholesalePrice REAL NOT NULL DEFAULT 0,
          retailPrice REAL NOT NULL DEFAULT 0,
          minStockQuantity INTEGER NOT NULL DEFAULT 0,
          barcode TEXT UNIQUE,
          warehouseQuantity INTEGER NOT NULL DEFAULT 0,
          storeQuantity INTEGER NOT NULL DEFAULT 0
        )
      ''');
    }

    if (oldVersion < 4) {
      // Add purchase_batches table for FIFO inventory management
      await db.execute('DROP TABLE IF EXISTS purchase_batches');
      await db.execute('''
        CREATE TABLE purchase_batches (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          productId INTEGER NOT NULL,
          purchaseDate TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          unitPurchasePrice REAL NOT NULL,
          remainingQuantity INTEGER NOT NULL,
          isActive INTEGER NOT NULL DEFAULT 1,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 5) {
      // Add internal_transfers table for warehouse to store transfers
      await db.execute('DROP TABLE IF EXISTS internal_transfers');
      await db.execute('''
        CREATE TABLE internal_transfers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          productId INTEGER NOT NULL,
          transferDate TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          retailPriceAtTransfer REAL NOT NULL,
          costAtTransfer REAL NOT NULL,
          totalValue REAL NOT NULL,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 6) {
      // Add orders, order_items, and app_notifications tables
      await db.execute('DROP TABLE IF EXISTS orders');
      await db.execute('DROP TABLE IF EXISTS order_items');
      await db.execute('DROP TABLE IF EXISTS app_notifications');

      await db.execute('''
        CREATE TABLE orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          orderDate TEXT NOT NULL,
          totalEstimatedCost REAL NOT NULL,
          notes TEXT,
          status TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          orderId INTEGER NOT NULL,
          productId INTEGER NOT NULL,
          quantity INTEGER NOT NULL,
          estimatedUnitPrice REAL NOT NULL,
          FOREIGN KEY (orderId) REFERENCES orders(id) ON DELETE CASCADE,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');

      await db.execute('''
        CREATE TABLE app_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          type TEXT NOT NULL,
          message TEXT NOT NULL,
          date TEXT NOT NULL,
          relatedEntityId INTEGER,
          relatedEntityType TEXT,
          suggestedAction TEXT,
          suggestedActionRoute TEXT,
          isRead INTEGER NOT NULL DEFAULT 0,
          isActionTaken INTEGER NOT NULL DEFAULT 0
        )
      ''');
    }

    if (oldVersion < 7) {
      // Add sales, purchases, and customer_accounts tables
      await db.execute('DROP TABLE IF EXISTS sales');
      await db.execute('DROP TABLE IF EXISTS sale_items');
      await db.execute('DROP TABLE IF EXISTS purchases');
      await db.execute('DROP TABLE IF EXISTS purchase_items');
      await db.execute('DROP TABLE IF EXISTS customer_accounts');

      await db.execute('''
        CREATE TABLE sales (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER,
          saleDate TEXT NOT NULL,
          totalAmount REAL NOT NULL,
          totalPaidAmount REAL NOT NULL DEFAULT 0,
          paymentMethod TEXT NOT NULL,
          notes TEXT,
          status TEXT NOT NULL,
          FOREIGN KEY (customerId) REFERENCES customers(id) ON DELETE SET NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE sale_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          saleId INTEGER NOT NULL,
          productId INTEGER,
          quantity INTEGER,
          unitPrice REAL NOT NULL,
          itemType TEXT NOT NULL,
          description TEXT,
          FOREIGN KEY (saleId) REFERENCES sales(id) ON DELETE CASCADE,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');

      await db.execute('''
        CREATE TABLE purchases (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          supplierId INTEGER,
          purchaseDate TEXT NOT NULL,
          totalAmount REAL NOT NULL,
          totalPaidAmount REAL NOT NULL DEFAULT 0,
          paymentMethod TEXT NOT NULL,
          notes TEXT,
          status TEXT NOT NULL,
          FOREIGN KEY (supplierId) REFERENCES suppliers(id) ON DELETE SET NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE purchase_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          purchaseId INTEGER NOT NULL,
          productId INTEGER NOT NULL,
          quantity INTEGER NOT NULL,
          unitPrice REAL NOT NULL,
          FOREIGN KEY (purchaseId) REFERENCES purchases(id) ON DELETE CASCADE,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');

      await db.execute('''
        CREATE TABLE customer_accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL,
          transactionDate TEXT NOT NULL,
          type TEXT NOT NULL,
          amount REAL NOT NULL,
          description TEXT,
          relatedInvoiceId INTEGER,
          isPaid INTEGER NOT NULL DEFAULT 0,
          FOREIGN KEY (customerId) REFERENCES customers(id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 8) {
      // Add inventory_counts table
      await db.execute('DROP TABLE IF EXISTS inventory_counts');
      await db.execute('''
        CREATE TABLE inventory_counts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          productId INTEGER NOT NULL,
          countDate TEXT NOT NULL,
          countedWarehouseQuantity INTEGER NOT NULL,
          countedStoreQuantity INTEGER NOT NULL,
          systemWarehouseQuantity INTEGER NOT NULL,
          systemStoreQuantity INTEGER NOT NULL,
          warehouseDifference INTEGER NOT NULL,
          storeDifference INTEGER NOT NULL,
          notes TEXT,
          FOREIGN KEY (productId) REFERENCES products(id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 9) {
      // Add customers, suppliers, and expenses tables
      await db.execute('DROP TABLE IF EXISTS customers');
      await db.execute('DROP TABLE IF EXISTS suppliers');
      await db.execute('DROP TABLE IF EXISTS expenses');

      await db.execute('''
        CREATE TABLE customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          email TEXT,
          address TEXT,
          credit_limit REAL NOT NULL DEFAULT 0.0,
          current_balance REAL NOT NULL DEFAULT 0.0,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE suppliers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          email TEXT,
          address TEXT,
          contact_person TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          description TEXT NOT NULL,
          amount REAL NOT NULL,
          category TEXT NOT NULL,
          expense_date TEXT NOT NULL,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
    }

    if (oldVersion < 10) {
      // Add customer_account_entries table
      await db.execute('DROP TABLE IF EXISTS customer_account_entries');
      await db.execute('''
        CREATE TABLE customer_account_entries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          type TEXT NOT NULL,
          description TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 11) {
      // Add database indexes for better performance
      await _createIndexes(db);
    }

    if (oldVersion < 12) {
      // Update sales and purchases tables for payment tracking
      // Add payment_receipts table

      // Update sales table - add totalPaidAmount column and copy data from paidAmount
      await db.execute(
        'ALTER TABLE sales ADD COLUMN totalPaidAmount REAL DEFAULT 0',
      );
      await db.execute(
        'UPDATE sales SET totalPaidAmount = COALESCE(paidAmount, 0)',
      );

      // Update purchases table - add totalPaidAmount column and copy data from paidAmount
      await db.execute(
        'ALTER TABLE purchases ADD COLUMN totalPaidAmount REAL DEFAULT 0',
      );
      await db.execute(
        'UPDATE purchases SET totalPaidAmount = COALESCE(paidAmount, 0)',
      );

      // Create payment_receipts table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS payment_receipts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          relatedEntityId INTEGER,
          relatedEntityType TEXT NOT NULL,
          transactionDate TEXT NOT NULL,
          amount REAL NOT NULL,
          type TEXT NOT NULL,
          paymentMethod TEXT NOT NULL,
          description TEXT,
          relatedInvoiceId INTEGER,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');

      // Add indexes for payment_receipts table
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_entity ON payment_receipts(relatedEntityId, relatedEntityType)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_date ON payment_receipts(transactionDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_invoice ON payment_receipts(relatedInvoiceId)',
      );
    }

    if (oldVersion < 13) {
      // المهمة 1.9: حذف عمود current_balance من جدول customers
      // هذا العمود لم يعد مستخدماً بعد إعادة هيكلة حساب الرصيد ليصبح ديناميكياً
      // نستخدم طريقة إعادة بناء الجدول لأن DROP COLUMN قد لا يكون مدعوماً في SQLite القديم
      try {
        // إنشاء جدول مؤقت بدون عمود current_balance
        await db.execute('''
          CREATE TABLE customers_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            credit_limit REAL NOT NULL DEFAULT 0.0,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        ''');

        // نسخ البيانات من الجدول القديم إلى الجديد (بدون current_balance)
        await db.execute('''
          INSERT INTO customers_new (id, name, phone, email, address, credit_limit, notes, created_at, updated_at)
          SELECT id, name, phone, email, address, credit_limit, notes, created_at, updated_at
          FROM customers
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE customers');

        // إعادة تسمية الجدول الجديد
        await db.execute('ALTER TABLE customers_new RENAME TO customers');
      } catch (e) {
        // إذا فشلت العملية، نتجاهل الخطأ
        // هذا يحدث عادة مع المستخدمين الجدد الذين لم يكن لديهم العمود أصلاً
        // تجاهل الخطأ بصمت
      }
    }

    if (oldVersion < 14) {
      // المهمة رقم 1: إعادة تصميم البنية الأساسية للفواتير
      // التأكد من أن جميع الجداول تحتوي على الحقول المطلوبة
      // هذا الإصدار يركز على التحقق من وجود الحقول المطلوبة وإضافتها إذا لزم الأمر

      try {
        // التحقق من جدول sales - يجب أن يحتوي على totalPaidAmount و status
        final salesColumns = await db.rawQuery("PRAGMA table_info(sales)");
        final salesColumnNames = salesColumns
            .map((col) => col['name'] as String)
            .toList();

        if (!salesColumnNames.contains('totalPaidAmount')) {
          await db.execute(
            'ALTER TABLE sales ADD COLUMN totalPaidAmount REAL NOT NULL DEFAULT 0.0',
          );
        }
        if (!salesColumnNames.contains('status')) {
          await db.execute(
            'ALTER TABLE sales ADD COLUMN status TEXT NOT NULL DEFAULT "completed"',
          );
        }

        // التحقق من جدول purchases - يجب أن يحتوي على totalPaidAmount و status
        final purchasesColumns = await db.rawQuery(
          "PRAGMA table_info(purchases)",
        );
        final purchasesColumnNames = purchasesColumns
            .map((col) => col['name'] as String)
            .toList();

        if (!purchasesColumnNames.contains('totalPaidAmount')) {
          await db.execute(
            'ALTER TABLE purchases ADD COLUMN totalPaidAmount REAL NOT NULL DEFAULT 0.0',
          );
        }
        if (!purchasesColumnNames.contains('status')) {
          await db.execute(
            'ALTER TABLE purchases ADD COLUMN status TEXT NOT NULL DEFAULT "completed"',
          );
        }

        // التحقق من جدول sale_items - يجب أن يحتوي على itemType
        final saleItemsColumns = await db.rawQuery(
          "PRAGMA table_info(sale_items)",
        );
        final saleItemsColumnNames = saleItemsColumns
            .map((col) => col['name'] as String)
            .toList();

        if (!saleItemsColumnNames.contains('itemType')) {
          await db.execute(
            'ALTER TABLE sale_items ADD COLUMN itemType TEXT NOT NULL DEFAULT "product"',
          );
        }
      } catch (e) {
        // تجاهل الأخطاء - قد تكون الحقول موجودة بالفعل
      }
    }

    if (oldVersion < 15) {
      // المهمة رقم 4: تطوير شامل لنموذج الفاتورة
      // إضافة حقول الخصم والملاحظات إلى جدول sales

      // التحقق من جدول sales - إضافة discountAmount و notes
      final salesColumns = await db.rawQuery("PRAGMA table_info(sales)");
      final salesColumnNames = salesColumns
          .map((col) => col['name'] as String)
          .toList();

      if (!salesColumnNames.contains('discountAmount')) {
        await db.execute(
          'ALTER TABLE sales ADD COLUMN discountAmount REAL NOT NULL DEFAULT 0.0',
        );
      }
      if (!salesColumnNames.contains('notes')) {
        await db.execute('ALTER TABLE sales ADD COLUMN notes TEXT');
      }
    }

    if (oldVersion < 16) {
      // المهمة رقم 5: تطوير شامل لنموذج فاتورة الشراء
      // إضافة حقول الخصم والملاحظات إلى جدول purchases

      // التحقق من جدول purchases - إضافة discountAmount و notes
      final purchasesColumns = await db.rawQuery(
        "PRAGMA table_info(purchases)",
      );
      final purchasesColumnNames = purchasesColumns
          .map((col) => col['name'] as String)
          .toList();

      if (!purchasesColumnNames.contains('discountAmount')) {
        await db.execute(
          'ALTER TABLE purchases ADD COLUMN discountAmount REAL NOT NULL DEFAULT 0.0',
        );
      }
      if (!purchasesColumnNames.contains('notes')) {
        await db.execute('ALTER TABLE purchases ADD COLUMN notes TEXT');
      }
    }

    // +++ إضافة شرط الترقية للإصدار 17 +++
    if (oldVersion < 17) {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS supplier_accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          supplierId INTEGER NOT NULL,
          transactionDate TEXT NOT NULL,
          type TEXT NOT NULL,
          amount REAL NOT NULL,
          description TEXT,
          relatedInvoiceId INTEGER,
          FOREIGN KEY (supplierId) REFERENCES suppliers(id) ON DELETE CASCADE
        )
      ''');
    }
    // --- نهاية الإضافة ---

    // *** تم حذف migrations الإصدارات 18-21 لتبسيط المشروع ***
  }

  Future<void> _createIndexes(Database db) async {
    try {
      // Products table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)',
      );

      // Sales table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(saleDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customerId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sales_payment_method ON sales(paymentMethod)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(status)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sales_date_customer ON sales(saleDate, customerId)',
      );

      // Purchases table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchaseDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchases_supplier ON purchases(supplierId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchases_payment_method ON purchases(paymentMethod)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchases_status ON purchases(status)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchases_date_supplier ON purchases(purchaseDate, supplierId)',
      );

      // Expenses table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(expense_date)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)',
      );

      // Orders table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(orderDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)',
      );

      // Customer accounts table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_customer_accounts_date ON customer_accounts(transactionDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_customer_accounts_customer ON customer_accounts(customerId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_customer_accounts_type ON customer_accounts(type)',
      );

      // Purchase batches table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchase_batches_product ON purchase_batches(productId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchase_batches_date ON purchase_batches(purchaseDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchase_batches_active ON purchase_batches(isActive)',
      );

      // Sale items table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(saleId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(productId)',
      );

      // Purchase items table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchase_items_purchase ON purchase_items(purchaseId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_purchase_items_product ON purchase_items(productId)',
      );

      // Notifications table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_notifications_date ON app_notifications(date)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_notifications_read ON app_notifications(isRead)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_notifications_type ON app_notifications(type)',
      );

      // Supplier accounts table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_supplier_accounts_date ON supplier_accounts(transactionDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_supplier_accounts_supplier ON supplier_accounts(supplierId)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_supplier_accounts_type ON supplier_accounts(type)',
      );

      // Payment receipts table indexes
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_entity ON payment_receipts(relatedEntityId, relatedEntityType)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_date ON payment_receipts(transactionDate)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_receipts_type ON payment_receipts(type)',
      );
    } catch (e) {
      // Ignore index creation errors (they might already exist)
    }
  }

  Future<void> _insertDefaultCategories(Database db) async {
    final defaultCategories = [
      'إلكترونيات',
      'ملابس',
      'طعام ومشروبات',
      'مستحضرات تجميل',
      'أدوات منزلية',
      'كتب وقرطاسية',
      'ألعاب',
      'رياضة',
      'صحة وعناية شخصية',
      'أخرى',
    ];

    for (final categoryName in defaultCategories) {
      try {
        await db.insert('categories', {'name': categoryName});
      } catch (e) {
        // Category might already exist, ignore error
      }
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // *** تم حذف _createAdvancedIndexes لتبسيط المشروع ***

  // *** تم حذف _initializePerformanceData لتبسيط المشروع ***

  // *** تم حذف cleanupOldData لتبسيط المشروع ***

  // *** تم حذف getDatabaseStats لتبسيط المشروع ***

  /// التحقق من سلامة قاعدة البيانات وإصلاح المشاكل
  Future<Map<String, dynamic>> validateAndFixDatabase() async {
    final db = await database;
    final issues = <String, dynamic>{};
    final fixes = <String>[];

    try {
      // التحقق من وجود الأعمدة الحرجة
      final salesColumns = await db.rawQuery("PRAGMA table_info(sales)");
      final salesColumnNames = salesColumns
          .map((col) => col['name'] as String)
          .toList();

      if (!salesColumnNames.contains('discountAmount')) {
        issues['sales_missing_discountAmount'] = true;
        await db.execute(
          'ALTER TABLE sales ADD COLUMN discountAmount REAL NOT NULL DEFAULT 0.0',
        );
        fixes.add('تم إضافة عمود discountAmount إلى جدول sales');
      }

      if (!salesColumnNames.contains('notes')) {
        issues['sales_missing_notes'] = true;
        await db.execute('ALTER TABLE sales ADD COLUMN notes TEXT');
        fixes.add('تم إضافة عمود notes إلى جدول sales');
      }

      // التحقق من جدول المشتريات
      final purchasesColumns = await db.rawQuery(
        "PRAGMA table_info(purchases)",
      );
      final purchasesColumnNames = purchasesColumns
          .map((col) => col['name'] as String)
          .toList();

      if (!purchasesColumnNames.contains('discountAmount')) {
        issues['purchases_missing_discountAmount'] = true;
        await db.execute(
          'ALTER TABLE purchases ADD COLUMN discountAmount REAL NOT NULL DEFAULT 0.0',
        );
        fixes.add('تم إضافة عمود discountAmount إلى جدول purchases');
      }

      if (!purchasesColumnNames.contains('notes')) {
        issues['purchases_missing_notes'] = true;
        await db.execute('ALTER TABLE purchases ADD COLUMN notes TEXT');
        fixes.add('تم إضافة عمود notes إلى جدول purchases');
      }

      // التحقق من الكميات السالبة
      final negativeQuantities = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM products
        WHERE warehouseQuantity < 0 OR storeQuantity < 0
      ''');

      if ((negativeQuantities.first['count'] as int) > 0) {
        issues['negative_quantities'] = negativeQuantities.first['count'];
        await db.execute(
          'UPDATE products SET warehouseQuantity = 0 WHERE warehouseQuantity < 0',
        );
        await db.execute(
          'UPDATE products SET storeQuantity = 0 WHERE storeQuantity < 0',
        );
        fixes.add('تم إصلاح الكميات السالبة');
      }

      // التحقق من المبيعات بدون عناصر
      final orphanedSales = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.saleId
        WHERE si.saleId IS NULL
      ''');

      if ((orphanedSales.first['count'] as int) > 0) {
        issues['orphaned_sales'] = orphanedSales.first['count'];
      }

      return {
        'status': 'success',
        'issues_found': issues.isNotEmpty,
        'issues': issues,
        'fixes_applied': fixes,
        'database_version': 20,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  // Reset database - تهيئة قاعدة البيانات بالكامل
  Future<void> resetDatabase() async {
    final db = await database;
    await db.close(); // إغلاق الاتصال الحالي
    _database = null; // تفريغ الـ singleton instance

    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'market.db');
    if (await databaseFactory.databaseExists(path)) {
      await deleteDatabase(path); // حذف ملف قاعدة البيانات
    }

    // إعادة تهيئة قاعدة البيانات (ستقوم _initDatabase بإنشاء الجداول وإدخال الفئات الافتراضية)
    _database = await _initDatabase();
    // إعادة تهيئة كل Providers في GetIt إذا لزم الأمر أو الاعتماد على إعادة تشغيل التطبيق.
    // بما أننا نعيد تشغيل MyApp، فإن Providers سيعاد إنشاؤها.
  }
}
