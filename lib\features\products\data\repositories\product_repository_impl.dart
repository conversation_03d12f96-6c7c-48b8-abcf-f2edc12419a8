import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/repositories/product_repository.dart';
import 'package:market/features/products/data/datasources/product_database_service.dart';
import 'package:market/features/products/data/models/product_model.dart';

class ProductRepositoryImpl implements ProductRepository {
  final ProductDatabaseService _databaseService;

  ProductRepositoryImpl(this._databaseService);

  @override
  Future<List<Product>> getProducts() async {
    try {
      final productModels = await _databaseService.getProducts();
      return productModels.map((model) => Product.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get products: $e');
    }
  }

  @override
  Future<Product?> getProductById(int id) async {
    try {
      final productModel = await _databaseService.getProductById(id);
      if (productModel != null) {
        return Product.fromModel(productModel);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product by id: $e');
    }
  }

  @override
  Future<void> createProduct(Product product) async {
    try {
      final productModel = ProductModel(
        name: product.name,
        description: product.description,
        category: product.category,
        unit: product.unit,
        lastPurchasePrice: product.lastPurchasePrice,
        wholesalePrice: product.wholesalePrice,
        retailPrice: product.retailPrice,
        minStockQuantity: product.minStockQuantity,
        barcode: product.barcode,
        warehouseQuantity: product.warehouseQuantity,
        storeQuantity: product.storeQuantity,
      );

      await _databaseService.createProduct(productModel);
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  @override
  Future<void> updateProduct(Product product) async {
    try {
      final productModel = ProductModel(
        id: product.id,
        name: product.name,
        description: product.description,
        category: product.category,
        unit: product.unit,
        lastPurchasePrice: product.lastPurchasePrice,
        wholesalePrice: product.wholesalePrice,
        retailPrice: product.retailPrice,
        minStockQuantity: product.minStockQuantity,
        barcode: product.barcode,
        warehouseQuantity: product.warehouseQuantity,
        storeQuantity: product.storeQuantity,
      );

      await _databaseService.updateProduct(productModel);
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  @override
  Future<void> deleteProduct(int id) async {
    try {
      await _databaseService.deleteProduct(id);
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  @override
  Future<List<Product>> searchProducts(String query) async {
    try {
      final productModels = await _databaseService.searchProducts(query);
      return productModels.map((model) => Product.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  @override
  Future<List<Product>> getProductsByCategory(String category) async {
    try {
      final productModels = await _databaseService.getProductsByCategory(
        category,
      );
      return productModels.map((model) => Product.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get products by category: $e');
    }
  }

  @override
  Future<List<Product>> getLowStockProducts() async {
    try {
      final productModels = await _databaseService.getLowStockProducts();
      return productModels.map((model) => Product.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get low stock products: $e');
    }
  }
}
