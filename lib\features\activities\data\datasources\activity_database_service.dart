import '../../../../core/database/database_service.dart';
import '../models/activity_model.dart';

class ActivityDatabaseService {
  final DatabaseService _databaseService;

  ActivityDatabaseService(this._databaseService);

  /// Get activities from multiple tables using UNION ALL
  Future<List<ActivityModel>> getActivities({
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      final db = await _databaseService.database;

      // Complex UNION ALL query to get activities from different tables
      const query = '''
        SELECT * FROM (
          SELECT
            s.id,
            'sale' as type,
            s.saleDate as date,
            'فاتورة بيع رقم ' || s.id || ' لـ ' || COALESCE(c.name, 'عميل عابر') || ' - إجمالي: ' || s.totalAmount || ' ر.ي (' || s.paymentMethod || ')' as description,
            s.id as relatedId,
            s.totalAmount as amount,
            1 as canEdit,
            '/sales/view/' || s.id as targetRoute,
            0 as isModified,
            s.paymentMethod as paymentMethod
          FROM sales s
          LEFT JOIN customers c ON s.customerId = c.id

          UNION ALL

          SELECT
            p.id,
            'purchase' as type,
            p.purchaseDate as date,
            'فاتورة شراء رقم ' || p.id || ' من ' || COALESCE(s.name, 'مورد غير محدد') || ' - إجمالي: ' || p.totalAmount || ' ر.ي (' || p.paymentMethod || ')' as description,
            p.id as relatedId,
            p.totalAmount as amount,
            1 as canEdit,
            '/purchases/view/' || p.id as targetRoute,
            0 as isModified,
            p.paymentMethod as paymentMethod
          FROM purchases p
          LEFT JOIN suppliers s ON p.supplierId = s.id

          UNION ALL

          SELECT
            id,
            'transfer' as type,
            transferDate as date,
            'تحويل داخلي للمنتج رقم ' || productId || ' - كمية: ' || quantity as description,
            id as relatedId,
            totalValue as amount,
            1 as canEdit,
            '/transfers' as targetRoute,
            0 as isModified,
            NULL as paymentMethod
          FROM internal_transfers

          UNION ALL

          SELECT
            id,
            'order' as type,
            orderDate as date,
            'طلبية شراء رقم ' || id || ' - ' || COALESCE(notes, 'بدون ملاحظات') as description,
            id as relatedId,
            totalEstimatedCost as amount,
            1 as canEdit,
            '/orders/view/' || id as targetRoute,
            0 as isModified,
            NULL as paymentMethod
          FROM orders

          UNION ALL

          SELECT
            id,
            'notification' as type,
            date as date,
            message as description,
            relatedEntityId as relatedId,
            NULL as amount,
            0 as canEdit,
            suggestedActionRoute as targetRoute,
            isActionTaken as isModified,
            NULL as paymentMethod
          FROM app_notifications

          UNION ALL

          SELECT
            pr.id,
            'payment_receipt' as type,
            pr.transactionDate as date,
            CASE pr.type
              WHEN 'payment_in' THEN 'سند قبض رقم ' || pr.id || ' من ' || COALESCE(c.name, 'عميل عابر') || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
              WHEN 'payment_out' THEN 'سند دفع رقم ' || pr.id || ' لـ ' || COALESCE(s.name, 'مورد غير محدد') || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
              ELSE 'سند رقم ' || pr.id || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
            END as description,
            pr.relatedEntityId as relatedId,
            pr.amount as amount,
            1 as canEdit,
            '/transactions/receipts/' || pr.id as targetRoute,
            0 as isModified,
            pr.paymentMethod as paymentMethod
          FROM payment_receipts pr
          LEFT JOIN customers c ON pr.relatedEntityType = 'customer' AND pr.relatedEntityId = c.id
          LEFT JOIN suppliers s ON pr.relatedEntityType = 'supplier' AND pr.relatedEntityId = s.id

          UNION ALL

          SELECT
            id,
            'inventory_count' as type,
            countDate as date,
            'جرد مخزون للمنتج رقم ' || productId ||
            CASE
              WHEN (warehouseDifference != 0 OR storeDifference != 0) THEN ' - يوجد فروقات'
              ELSE ' - متطابق'
            END as description,
            productId as relatedId,
            NULL as amount,
            1 as canEdit,
            '/inventory_count/' || id as targetRoute,
            CASE WHEN (warehouseDifference != 0 OR storeDifference != 0) THEN 1 ELSE 0 END as isModified,
            NULL as paymentMethod
          FROM inventory_counts
        )
        ORDER BY date DESC
        LIMIT ? OFFSET ?
      ''';

      final List<Map<String, dynamic>> maps = await db.rawQuery(query, [
        limit,
        offset,
      ]);

      return List.generate(maps.length, (i) {
        return ActivityModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get activities: $e');
    }
  }

  /// Get activities count for pagination
  Future<int> getActivitiesCount() async {
    try {
      final db = await _databaseService.database;

      const query = '''
        SELECT COUNT(*) as count FROM (
          SELECT id FROM sales
          UNION ALL
          SELECT id FROM purchases
          UNION ALL
          SELECT id FROM internal_transfers
          UNION ALL
          SELECT id FROM orders
          UNION ALL
          SELECT id FROM app_notifications
          UNION ALL
          SELECT id FROM payment_receipts
          UNION ALL
          SELECT id FROM inventory_counts
        )
      ''';

      final List<Map<String, dynamic>> result = await db.rawQuery(query);
      return result.first['count'] as int;
    } catch (e) {
      throw Exception('Failed to get activities count: $e');
    }
  }

  /// Get activities by type
  Future<List<ActivityModel>> getActivitiesByType(
    String type, {
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      final db = await _databaseService.database;

      String query = '';
      List<dynamic> args = [];

      switch (type) {
        case 'transfer':
          query = '''
            SELECT 
              id,
              'transfer' as type,
              transferDate as date,
              'تحويل داخلي للمنتج رقم ' || productId || ' - كمية: ' || quantity as description,
              id as relatedId,
              totalValue as amount,
              1 as canEdit,
              '/transfers' as targetRoute,
              0 as isModified
            FROM internal_transfers
            ORDER BY transferDate DESC 
            LIMIT ? OFFSET ?
          ''';
          args = [limit, offset];
          break;

        case 'order':
          query = '''
            SELECT 
              id,
              'order' as type,
              orderDate as date,
              'طلبية شراء رقم ' || id || ' - ' || COALESCE(notes, 'بدون ملاحظات') as description,
              id as relatedId,
              totalEstimatedCost as amount,
              1 as canEdit,
              '/orders/view/' || id as targetRoute,
              0 as isModified
            FROM orders
            ORDER BY orderDate DESC 
            LIMIT ? OFFSET ?
          ''';
          args = [limit, offset];
          break;

        case 'notification':
          query = '''
            SELECT 
              id,
              'notification' as type,
              date as date,
              message as description,
              relatedEntityId as relatedId,
              NULL as amount,
              0 as canEdit,
              suggestedActionRoute as targetRoute,
              isActionTaken as isModified
            FROM app_notifications
            ORDER BY date DESC 
            LIMIT ? OFFSET ?
          ''';
          args = [limit, offset];
          break;

        default:
          throw Exception('Unknown activity type: $type');
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);

      return List.generate(maps.length, (i) {
        return ActivityModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get activities by type: $e');
    }
  }

  /// Get recent activities (last 7 days)
  Future<List<ActivityModel>> getRecentActivities({int limit = 20}) async {
    try {
      final db = await _databaseService.database;
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

      const query = '''
        SELECT * FROM (
          SELECT
            s.id,
            'sale' as type,
            s.saleDate as date,
            'فاتورة بيع رقم ' || s.id || ' لـ ' || COALESCE(c.name, 'عميل عابر') || ' - إجمالي: ' || s.totalAmount || ' ر.ي (' || s.paymentMethod || ')' as description,
            s.id as relatedId,
            s.totalAmount as amount,
            1 as canEdit,
            '/sales/view/' || s.id as targetRoute,
            0 as isModified,
            s.paymentMethod as paymentMethod
          FROM sales s
          LEFT JOIN customers c ON s.customerId = c.id
          WHERE s.saleDate >= ?

          UNION ALL

          SELECT
            p.id,
            'purchase' as type,
            p.purchaseDate as date,
            'فاتورة شراء رقم ' || p.id || ' من ' || COALESCE(s.name, 'مورد غير محدد') || ' - إجمالي: ' || p.totalAmount || ' ر.ي (' || p.paymentMethod || ')' as description,
            p.id as relatedId,
            p.totalAmount as amount,
            1 as canEdit,
            '/purchases/view/' || p.id as targetRoute,
            0 as isModified,
            p.paymentMethod as paymentMethod
          FROM purchases p
          LEFT JOIN suppliers s ON p.supplierId = s.id
          WHERE p.purchaseDate >= ?

          UNION ALL

          SELECT
            id,
            'transfer' as type,
            transferDate as date,
            'تحويل داخلي للمنتج رقم ' || productId || ' - كمية: ' || quantity as description,
            id as relatedId,
            totalValue as amount,
            1 as canEdit,
            '/transfers' as targetRoute,
            0 as isModified,
            NULL as paymentMethod
          FROM internal_transfers
          WHERE transferDate >= ?

          UNION ALL

          SELECT
            id,
            'order' as type,
            orderDate as date,
            'طلبية شراء رقم ' || id || ' - ' || COALESCE(notes, 'بدون ملاحظات') as description,
            id as relatedId,
            totalEstimatedCost as amount,
            1 as canEdit,
            '/orders/view/' || id as targetRoute,
            0 as isModified,
            NULL as paymentMethod
          FROM orders
          WHERE orderDate >= ?

          UNION ALL

          SELECT
            id,
            'notification' as type,
            date as date,
            message as description,
            relatedEntityId as relatedId,
            NULL as amount,
            0 as canEdit,
            suggestedActionRoute as targetRoute,
            isActionTaken as isModified,
            NULL as paymentMethod
          FROM app_notifications
          WHERE date >= ?

          UNION ALL

          SELECT
            pr.id,
            'payment_receipt' as type,
            pr.transactionDate as date,
            CASE pr.type
              WHEN 'payment_in' THEN 'سند قبض رقم ' || pr.id || ' من ' || COALESCE(c.name, 'عميل عابر') || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
              WHEN 'payment_out' THEN 'سند دفع رقم ' || pr.id || ' لـ ' || COALESCE(s.name, 'مورد غير محدد') || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
              ELSE 'سند رقم ' || pr.id || ' - مبلغ: ' || pr.amount || ' ر.ي (' || pr.paymentMethod || ')'
            END as description,
            pr.relatedEntityId as relatedId,
            pr.amount as amount,
            1 as canEdit,
            '/transactions/receipts/' || pr.id as targetRoute,
            0 as isModified,
            pr.paymentMethod as paymentMethod
          FROM payment_receipts pr
          LEFT JOIN customers c ON pr.relatedEntityType = 'customer' AND pr.relatedEntityId = c.id
          LEFT JOIN suppliers s ON pr.relatedEntityType = 'supplier' AND pr.relatedEntityId = s.id
          WHERE pr.transactionDate >= ?

          UNION ALL

          SELECT
            id,
            'inventory_count' as type,
            countDate as date,
            'جرد مخزون للمنتج رقم ' || productId ||
            CASE
              WHEN (warehouseDifference != 0 OR storeDifference != 0) THEN ' - يوجد فروقات'
              ELSE ' - متطابق'
            END as description,
            productId as relatedId,
            NULL as amount,
            1 as canEdit,
            '/inventory_count/' || id as targetRoute,
            CASE WHEN (warehouseDifference != 0 OR storeDifference != 0) THEN 1 ELSE 0 END as isModified,
            NULL as paymentMethod
          FROM inventory_counts
          WHERE countDate >= ?
        )
        ORDER BY date DESC
        LIMIT ?
      ''';

      final List<Map<String, dynamic>> maps = await db.rawQuery(query, [
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        sevenDaysAgo.toIso8601String(),
        limit,
      ]);

      return List.generate(maps.length, (i) {
        return ActivityModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get recent activities: $e');
    }
  }

  /// Get activity statistics
  Future<Map<String, dynamic>> getActivityStatistics() async {
    try {
      final db = await _databaseService.database;

      // Get counts by type
      final salesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM sales',
      );
      final purchasesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM purchases',
      );
      final transfersCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM internal_transfers',
      );
      final ordersCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM orders',
      );
      final notificationsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_notifications',
      );
      final customerAccountsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM customer_accounts',
      );
      final paymentReceiptsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM payment_receipts',
      );
      final inventoryCountsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM inventory_counts',
      );

      // Get total activities today
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      const todayQuery = '''
        SELECT COUNT(*) as count FROM (
          SELECT id FROM sales WHERE saleDate >= ?
          UNION ALL
          SELECT id FROM purchases WHERE purchaseDate >= ?
          UNION ALL
          SELECT id FROM internal_transfers WHERE transferDate >= ?
          UNION ALL
          SELECT id FROM orders WHERE orderDate >= ?
          UNION ALL
          SELECT id FROM app_notifications WHERE date >= ?
          UNION ALL
          SELECT id FROM payment_receipts WHERE transactionDate >= ?
          UNION ALL
          SELECT id FROM inventory_counts WHERE countDate >= ?
        )
      ''';

      final todayResult = await db.rawQuery(todayQuery, [
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
        todayStart.toIso8601String(),
      ]);

      return {
        'salesCount': salesCount.first['count'] as int,
        'purchasesCount': purchasesCount.first['count'] as int,
        'transfersCount': transfersCount.first['count'] as int,
        'ordersCount': ordersCount.first['count'] as int,
        'notificationsCount': notificationsCount.first['count'] as int,
        'customerAccountsCount': customerAccountsCount.first['count'] as int,
        'paymentReceiptsCount': paymentReceiptsCount.first['count'] as int,
        'inventoryCountsCount': inventoryCountsCount.first['count'] as int,
        'todayCount': todayResult.first['count'] as int,
        'totalCount':
            (salesCount.first['count'] as int) +
            (purchasesCount.first['count'] as int) +
            (transfersCount.first['count'] as int) +
            (ordersCount.first['count'] as int) +
            (notificationsCount.first['count'] as int) +
            (customerAccountsCount.first['count'] as int) +
            (paymentReceiptsCount.first['count'] as int) +
            (inventoryCountsCount.first['count'] as int),
      };
    } catch (e) {
      throw Exception('Failed to get activity statistics: $e');
    }
  }
}
