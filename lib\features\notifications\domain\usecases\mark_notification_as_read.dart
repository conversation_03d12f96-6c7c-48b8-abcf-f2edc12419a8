import '../repositories/notification_repository.dart';

class MarkNotificationAsReadUseCase {
  final NotificationRepository _repository;

  MarkNotificationAsReadUseCase(this._repository);

  Future<void> call(int id) async {
    if (id <= 0) {
      throw Exception('Notification ID must be greater than 0');
    }

    try {
      await _repository.markNotificationAsRead(id);
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }
}
