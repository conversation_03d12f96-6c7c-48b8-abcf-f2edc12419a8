import '../entities/activity.dart';

abstract class ActivityRepository {
  /// Get activities with pagination
  Future<List<Activity>> getActivities({int limit = 10, int offset = 0});

  /// Get activities count for pagination
  Future<int> getActivitiesCount();

  /// Get activities by type
  Future<List<Activity>> getActivitiesByType(
    String type, {
    int limit = 10,
    int offset = 0,
  });

  /// Get recent activities (last 7 days)
  Future<List<Activity>> getRecentActivities({int limit = 20});

  /// Get activity statistics
  Future<Map<String, dynamic>> getActivityStatistics();
}
