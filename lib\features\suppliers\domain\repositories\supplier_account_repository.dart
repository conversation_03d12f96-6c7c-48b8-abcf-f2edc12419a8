import '../entities/supplier_account.dart';

abstract class SupplierAccountRepository {
  /// Add a new supplier account entry
  Future<int> addSupplierAccountEntry(SupplierAccount entry);

  /// Get supplier account statement
  Future<List<SupplierAccount>> getSupplierAccountStatement(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get all supplier account entries
  Future<List<SupplierAccount>> getAllSupplierAccountEntries();

  /// Delete supplier account entry
  Future<void> deleteSupplierAccountEntry(int id);

  /// Delete all entries for a supplier
  Future<void> deleteAllEntriesForSupplier(int supplierId);

  /// Get supplier account entries (simplified method)
  Future<List<SupplierAccount>> getSupplierAccountEntries(int supplierId);

  /// Get supplier account balance
  Future<double> getSupplierAccountBalance(int supplierId);

  /// Delete supplier account entries (alias for deleteAllEntriesForSupplier)
  Future<void> deleteSupplierAccountEntries(int supplierId);
}
