import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/expense_provider.dart';
import '../../domain/entities/expense.dart';

class ExpenseFormScreen extends StatefulWidget {
  final String? expenseId;

  const ExpenseFormScreen({super.key, this.expenseId});

  @override
  State<ExpenseFormScreen> createState() => _ExpenseFormScreenState();
}

class _ExpenseFormScreenState extends State<ExpenseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _categoryController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  Expense? _existingExpense;

  final List<String> _expenseCategories = [
    'إيجار',
    'كهرباء',
    'ماء',
    'هاتف وإنترنت',
    'صيانة',
    'مواد تنظيف',
    'رواتب',
    'مواصلات',
    'قرطاسية',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.expenseId != null) {
      _loadExpense();
    }
  }

  Future<void> _loadExpense() async {
    setState(() => _isLoading = true);
    try {
      final expense = await context.read<ExpenseProvider>().getExpenseById(
        int.parse(widget.expenseId!),
      );
      if (expense != null) {
        setState(() {
          _existingExpense = expense;
          _descriptionController.text = expense.description;
          _amountController.text = expense.amount.toString();
          _categoryController.text = expense.category;
          _selectedDate = expense.expenseDate;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل بيانات المصروف: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _showAddCategoryDialog() async {
    final newCategoryController = TextEditingController();

    final result = await showDialog<String>(
      context: context, // السياق الخاص بالشاشة الرئيسية
      builder: (dialogContext) {
        // هذا هو السياق الخاص بالحوار
        return AlertDialog(
          title: const Text('إضافة فئة مصروف جديدة'),
          content: TextField(
            controller: newCategoryController,
            decoration: const InputDecoration(
              labelText: 'اسم الفئة',
              border: OutlineInputBorder(),
              hintText: 'أدخل اسم الفئة الجديدة',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () =>
                  Navigator.of(dialogContext).pop(), // استخدام dialogContext
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (newCategoryController.text.trim().isNotEmpty) {
                  Navigator.of(dialogContext).pop(
                    newCategoryController.text.trim(),
                  ); // استخدام dialogContext وإرجاع القيمة
                } else {
                  // لا تفعل شيئاً إذا كان النص فارغاً
                  // سيتم عرض رسالة خطأ خارج الحوار
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );

    if (result != null && result.trim().isNotEmpty && mounted) {
      // التحقق من mounted بعد await
      // هنا نقوم بإضافة الفئة إلى القائمة، وتحديث DropdownButtonFormField
      setState(() {
        if (!_expenseCategories.contains(result)) {
          _expenseCategories.add(result);
          _categoryController.text = result; // تحديد الفئة المضافة حديثاً
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              // استخدام context الخاص بالشاشة
              const SnackBar(content: Text('هذه الفئة موجودة بالفعل')),
            );
          }
          return;
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          // استخدام context الخاص بالشاشة
          SnackBar(content: Text('تم إضافة فئة "$result" بنجاح')),
        );
      }
    } else if (result != null && result.trim().isEmpty && mounted) {
      // إذا كان النص فارغاً، اعرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('اسم الفئة لا يمكن أن يكون فارغاً')),
      );
    }

    newCategoryController.dispose();
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final expense = Expense(
        description: _descriptionController.text.trim(),
        amount: double.parse(_amountController.text.trim()),
        category: _categoryController.text.trim(),
        expenseDate: _selectedDate,
        notes: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.expenseId != null) {
        // Update existing expense
        final updatedExpense = expense.copyWith(
          id: int.parse(widget.expenseId!),
        );
        await context.read<ExpenseProvider>().updateExpense(updatedExpense);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث المصروف بنجاح')),
          );
        }
      } else {
        // Create new expense
        await context.read<ExpenseProvider>().createExpense(expense);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة المصروف بنجاح')),
          );
        }
      }

      if (mounted) {
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ المصروف: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _existingExpense != null ? 'تعديل المصروف' : 'إضافة مصروف جديد',
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // التاريخ
              Card(
                child: ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('التاريخ'),
                  subtitle: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectDate,
                ),
              ),
              const SizedBox(height: 16),

              // الوصف
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المصروف *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال وصف المصروف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: 'ر.ي',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (double.parse(value) <= 0) {
                    return 'يجب أن يكون المبلغ أكبر من صفر';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // الفئة مع زر إضافة فئة جديدة
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _categoryController.text.isEmpty
                          ? null
                          : _categoryController.text,
                      decoration: const InputDecoration(
                        labelText: 'فئة المصروف *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      items: _expenseCategories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _categoryController.text = value ?? '';
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار فئة المصروف';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _showAddCategoryDialog,
                    icon: const Icon(Icons.add),
                    tooltip: 'إضافة فئة جديدة',
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // زر الحفظ
              ElevatedButton(
                onPressed: _isLoading ? null : _saveExpense,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        _existingExpense != null
                            ? 'تحديث المصروف'
                            : 'إضافة المصروف',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'ملاحظة: وحدة المصروفات قيد التطوير. هذا النموذج للعرض فقط.',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
