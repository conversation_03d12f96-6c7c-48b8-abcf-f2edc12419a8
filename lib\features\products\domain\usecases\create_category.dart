import 'package:market/features/products/domain/entities/category.dart';
import 'package:market/features/products/domain/repositories/category_repository.dart';

class CreateCategoryUseCase {
  final CategoryRepository _repository;

  CreateCategoryUseCase(this._repository);

  Future<void> call(Category category) async {
    // Validate category data
    if (category.name.trim().isEmpty) {
      throw Exception('Category name cannot be empty');
    }

    // Check if category already exists
    final exists = await _repository.categoryExists(category.name.trim());
    if (exists) {
      throw Exception('Category with this name already exists');
    }

    await _repository.createCategory(category);
  }
}
