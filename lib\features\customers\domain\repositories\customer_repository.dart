import '../entities/customer.dart';

abstract class CustomerRepository {
  Future<List<Customer>> getAllCustomers();
  Future<Customer?> getCustomerById(int id);
  Future<int> createCustomer(Customer customer);
  Future<void> updateCustomer(Customer customer);
  Future<void> deleteCustomer(int id);
  Future<List<Customer>> searchCustomers(String query);
  Future<void> addAccountEntry(
    int customerId,
    double amount,
    String type,
    String description,
  );
  Future<List<Map<String, dynamic>>> getAccountStatement(int customerId);
}
