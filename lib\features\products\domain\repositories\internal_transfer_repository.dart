import '../entities/internal_transfer.dart';

abstract class InternalTransferRepository {
  /// Add a new internal transfer
  Future<int> addTransfer(InternalTransfer transfer);

  /// Get all internal transfers
  Future<List<InternalTransfer>> getTransfers();

  /// Get a specific transfer by ID
  Future<InternalTransfer?> getTransferById(int id);

  /// Get all transfers for a specific product
  Future<List<InternalTransfer>> getTransfersByProductId(int productId);

  /// Get transfers within a date range
  Future<List<InternalTransfer>> getTransfersByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Get total transfer statistics
  Future<Map<String, dynamic>> getTransferStatistics();

  /// Delete all transfers for a product (used when product is deleted)
  Future<void> deleteTransfersForProduct(int productId);
}
