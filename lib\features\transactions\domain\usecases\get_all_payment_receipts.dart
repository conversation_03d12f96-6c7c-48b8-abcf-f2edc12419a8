import '../entities/payment_receipt.dart';
import '../repositories/payment_receipt_repository.dart';

/// Use case لجلب جميع سندات القبض والدفع
class GetAllPaymentReceiptsUseCase {
  final PaymentReceiptRepository _repository;

  GetAllPaymentReceiptsUseCase(this._repository);

  /// جلب جميع السندات
  Future<List<PaymentReceipt>> call() async {
    try {
      return await _repository.getAllPaymentReceipts();
    } catch (e) {
      throw Exception('فشل في جلب السندات: ${e.toString()}');
    }
  }

  /// جلب السندات حسب نوع الكيان (عميل أو مورد)
  Future<List<PaymentReceipt>> getByEntityType(String entityType) async {
    try {
      final allReceipts = await _repository.getAllPaymentReceipts();
      return allReceipts
          .where((receipt) => receipt.relatedEntityType == entityType)
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب السندات حسب نوع الكيان: ${e.toString()}');
    }
  }

  /// جلب السندات حسب الكيان المحدد
  Future<List<PaymentReceipt>> getByEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      return await _repository.getPaymentReceiptsByEntity(
        entityId: entityId,
        entityType: entityType,
      );
    } catch (e) {
      throw Exception('فشل في جلب السندات للكيان المحدد: ${e.toString()}');
    }
  }

  /// جلب السندات حسب نوع المعاملة (قبض أو دفع)
  Future<List<PaymentReceipt>> getByTransactionType(String type) async {
    try {
      final allReceipts = await _repository.getAllPaymentReceipts();
      return allReceipts.where((receipt) => receipt.type == type).toList();
    } catch (e) {
      throw Exception('فشل في جلب السندات حسب نوع المعاملة: ${e.toString()}');
    }
  }

  /// جلب السندات في فترة زمنية محددة
  Future<List<PaymentReceipt>> getByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final allReceipts = await _repository.getAllPaymentReceipts();
      return allReceipts.where((receipt) {
        final receiptDate = receipt.transactionDate;
        return receiptDate.isAfter(
              startDate.subtract(const Duration(days: 1)),
            ) &&
            receiptDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب السندات في الفترة المحددة: ${e.toString()}');
    }
  }
}
