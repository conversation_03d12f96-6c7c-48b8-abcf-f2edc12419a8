import '../../domain/entities/internal_transfer.dart';
import '../../domain/repositories/internal_transfer_repository.dart';
import '../datasources/internal_transfer_database_service.dart';
import '../models/internal_transfer_model.dart';

class InternalTransferRepositoryImpl implements InternalTransferRepository {
  final InternalTransferDatabaseService _databaseService;

  InternalTransferRepositoryImpl(this._databaseService);

  @override
  Future<int> addTransfer(InternalTransfer transfer) async {
    try {
      final transferModel = InternalTransferModel(
        id: transfer.id,
        productId: transfer.productId,
        transferDate: transfer.transferDate,
        quantity: transfer.quantity,
        retailPriceAtTransfer: transfer.retailPriceAtTransfer,
        costAtTransfer: transfer.costAtTransfer,
        totalValue: transfer.totalValue,
      );

      return await _databaseService.addTransfer(transferModel);
    } catch (e) {
      throw Exception('Failed to add transfer: $e');
    }
  }

  @override
  Future<List<InternalTransfer>> getTransfers() async {
    try {
      final transferModels = await _databaseService.getTransfers();
      return transferModels
          .map((model) => InternalTransfer.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get transfers: $e');
    }
  }

  @override
  Future<InternalTransfer?> getTransferById(int id) async {
    try {
      final transferModel = await _databaseService.getTransferById(id);
      return transferModel != null
          ? InternalTransfer.fromModel(transferModel)
          : null;
    } catch (e) {
      throw Exception('Failed to get transfer by id: $e');
    }
  }

  @override
  Future<List<InternalTransfer>> getTransfersByProductId(int productId) async {
    try {
      final transferModels = await _databaseService.getTransfersByProductId(
        productId,
      );
      return transferModels
          .map((model) => InternalTransfer.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get transfers for product: $e');
    }
  }

  @override
  Future<List<InternalTransfer>> getTransfersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final transferModels = await _databaseService.getTransfersByDateRange(
        startDate,
        endDate,
      );
      return transferModels
          .map((model) => InternalTransfer.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get transfers by date range: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getTransferStatistics() async {
    try {
      return await _databaseService.getTransferStatistics();
    } catch (e) {
      throw Exception('Failed to get transfer statistics: $e');
    }
  }

  @override
  Future<void> deleteTransfersForProduct(int productId) async {
    try {
      await _databaseService.deleteTransfersForProduct(productId);
    } catch (e) {
      throw Exception('Failed to delete transfers for product: $e');
    }
  }
}
