import '../entities/supplier.dart';
import '../repositories/supplier_repository.dart';

class CreateSupplier {
  final SupplierRepository _repository;

  CreateSupplier(this._repository);

  Future<int> call(Supplier supplier) async {
    // Validation
    if (supplier.name.trim().isEmpty) {
      throw Exception('Supplier name cannot be empty');
    }

    // Validate email format if provided
    if (supplier.email != null && supplier.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(supplier.email!)) {
        throw Exception('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (supplier.phone != null && supplier.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^[\d\s\-\+\(\)]+$');
      if (!phoneRegex.hasMatch(supplier.phone!)) {
        throw Exception('Invalid phone format');
      }
    }

    try {
      return await _repository.createSupplier(supplier);
    } catch (e) {
      throw Exception('Failed to create supplier: $e');
    }
  }
}
