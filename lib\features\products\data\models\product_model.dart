class ProductModel {
  final int? id;
  final String name;
  final String? description;
  final String category;
  final String unit;
  final double?
  lastPurchasePrice; // آخر سعر شراء للوحدة، يمكن أن يكون null في البداية
  final double wholesalePrice; // سعر البيع بالجملة
  final double retailPrice; // سعر البيع بالتجزئة
  final int minStockQuantity;
  final String? barcode;
  final int warehouseQuantity;
  final int storeQuantity;

  const ProductModel({
    this.id,
    required this.name,
    this.description,
    required this.category,
    required this.unit,
    this.lastPurchasePrice,
    required this.wholesalePrice,
    required this.retailPrice,
    required this.minStockQuantity,
    this.barcode,
    required this.warehouseQuantity,
    required this.storeQuantity,
  });

  // Convert from Map (from database)
  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      category: map['category'] as String,
      unit: map['unit'] as String,
      lastPurchasePrice: map['lastPurchasePrice'] != null
          ? (map['lastPurchasePrice'] as num).toDouble()
          : null,
      wholesalePrice: (map['wholesalePrice'] as num).toDouble(),
      retailPrice: (map['retailPrice'] as num).toDouble(),
      minStockQuantity: map['minStockQuantity'] as int,
      barcode: map['barcode'] as String?,
      warehouseQuantity: map['warehouseQuantity'] as int,
      storeQuantity: map['storeQuantity'] as int,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'unit': unit,
      'lastPurchasePrice': lastPurchasePrice,
      'wholesalePrice': wholesalePrice,
      'retailPrice': retailPrice,
      'minStockQuantity': minStockQuantity,
      'barcode': barcode,
      'warehouseQuantity': warehouseQuantity,
      'storeQuantity': storeQuantity,
    };
  }

  // Convert from JSON
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel.fromMap(json);
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return toMap();
  }

  // Copy with method for updates
  ProductModel copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    String? unit,
    double? lastPurchasePrice,
    double? wholesalePrice,
    double? retailPrice,
    int? minStockQuantity,
    String? barcode,
    int? warehouseQuantity,
    int? storeQuantity,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      lastPurchasePrice: lastPurchasePrice ?? this.lastPurchasePrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      retailPrice: retailPrice ?? this.retailPrice,
      minStockQuantity: minStockQuantity ?? this.minStockQuantity,
      barcode: barcode ?? this.barcode,
      warehouseQuantity: warehouseQuantity ?? this.warehouseQuantity,
      storeQuantity: storeQuantity ?? this.storeQuantity,
    );
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, category: $category, unit: $unit, '
        'lastPurchasePrice: $lastPurchasePrice, wholesalePrice: $wholesalePrice, '
        'retailPrice: $retailPrice, warehouseQuantity: $warehouseQuantity, storeQuantity: $storeQuantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
