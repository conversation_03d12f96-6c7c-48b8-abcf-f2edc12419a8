import '../entities/customer_account.dart';

abstract class CustomerAccountRepository {
  /// Add a new customer account entry
  Future<int> addCustomerAccountEntry(CustomerAccount entry);

  /// Get customer account statement
  Future<List<CustomerAccount>> getCustomerAccountStatement(
    int customerId, {
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get customer balance
  Future<double> getCustomerBalance(int customerId);

  /// Get unpaid entries for customer
  Future<List<CustomerAccount>> getUnpaidEntries(int customerId);

  /// Mark entry as paid
  Future<void> markEntryAsPaid(int entryId);

  /// Update customer account entry
  Future<void> updateCustomerAccountEntry(CustomerAccount entry);

  /// Delete customer account entry
  Future<void> deleteCustomerAccountEntry(int entryId);

  /// Get total payments received for customer
  Future<double> getTotalPaymentsReceivedForCustomer(int customerId);

  /// Get customer account statistics
  Future<Map<String, dynamic>> getCustomerAccountStatistics(int customerId);
}
