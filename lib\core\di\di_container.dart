import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:market/core/database/database_service.dart';

// Products imports
import 'package:market/features/products/data/datasources/product_database_service.dart';
import 'package:market/features/products/data/datasources/category_database_service.dart';
import 'package:market/features/products/data/repositories/product_repository_impl.dart';
import 'package:market/features/products/data/repositories/category_repository_impl.dart';
import 'package:market/features/products/domain/repositories/product_repository.dart';
import 'package:market/features/products/domain/repositories/category_repository.dart';
import 'package:market/features/products/domain/usecases/get_all_products.dart';
import 'package:market/features/products/domain/usecases/create_product.dart';

// Customers imports
import 'package:market/features/customers/data/datasources/customer_database_service.dart';
import 'package:market/features/customers/data/repositories/customer_repository_impl.dart';
import 'package:market/features/customers/domain/repositories/customer_repository.dart';
import 'package:market/features/customers/domain/usecases/create_customer.dart';
import 'package:market/features/customers/domain/usecases/update_customer.dart';
import 'package:market/features/customers/domain/usecases/get_all_customers.dart';
import 'package:market/features/customers/domain/usecases/get_customer_by_id.dart';
import 'package:market/features/customers/domain/usecases/delete_customer.dart';

// Suppliers imports
import 'package:market/features/suppliers/data/datasources/supplier_database_service.dart';
import 'package:market/features/suppliers/data/repositories/supplier_repository_impl.dart';
import 'package:market/features/suppliers/domain/repositories/supplier_repository.dart';
import 'package:market/features/suppliers/domain/usecases/create_supplier.dart';
import 'package:market/features/suppliers/domain/usecases/update_supplier.dart';
import 'package:market/features/suppliers/domain/usecases/get_all_suppliers.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_by_id.dart';
import 'package:market/features/suppliers/domain/usecases/delete_supplier.dart';

// Supplier Account imports
import 'package:market/features/suppliers/data/datasources/supplier_account_database_service.dart';
import 'package:market/features/suppliers/data/repositories/supplier_account_repository_impl.dart';
import 'package:market/features/suppliers/domain/repositories/supplier_account_repository.dart';
import 'package:market/features/suppliers/domain/usecases/add_supplier_account_entry.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_account_statement.dart';

// Expenses imports
import 'package:market/features/expenses/data/datasources/expense_database_service.dart';
import 'package:market/features/expenses/data/repositories/expense_repository_impl.dart';
import 'package:market/features/expenses/domain/repositories/expense_repository.dart';
import 'package:market/features/expenses/domain/usecases/create_expense.dart';
import 'package:market/features/expenses/domain/usecases/update_expense.dart';
import 'package:market/features/expenses/domain/usecases/get_all_expenses.dart';
import 'package:market/features/expenses/domain/usecases/get_expense_by_id.dart';
import 'package:market/features/expenses/domain/usecases/delete_expense.dart';

// Provider imports
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/presentation/providers/category_provider.dart';
import 'package:market/features/products/presentation/providers/internal_transfer_provider.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';
import 'package:market/features/sales/presentation/providers/sale_provider.dart';
import 'package:market/features/purchases/presentation/providers/purchase_provider.dart';
import 'package:market/features/orders/presentation/providers/order_provider.dart';
import 'package:market/features/notifications/presentation/providers/notification_provider.dart';
import 'package:market/features/activities/presentation/providers/activity_provider.dart';
import 'package:market/features/inventory_count/presentation/providers/inventory_count_provider.dart';
// *** تم حذف analytics_provider لتبسيط المشروع ***
import 'package:market/features/expenses/presentation/providers/expense_provider.dart';
import 'package:market/features/products/domain/usecases/update_product.dart';
import 'package:market/features/products/domain/usecases/delete_product.dart';
import 'package:market/features/products/domain/usecases/get_product_by_id.dart';
import 'package:market/features/products/domain/usecases/get_all_categories.dart';
import 'package:market/features/products/domain/usecases/create_category.dart';
import 'package:market/features/products/domain/usecases/update_category.dart';
import 'package:market/features/products/domain/usecases/delete_category.dart';

// Purchase Batch imports
import 'package:market/features/products/data/datasources/purchase_batch_database_service.dart';
import 'package:market/features/products/data/repositories/purchase_batch_repository_impl.dart';
import 'package:market/features/products/domain/repositories/purchase_batch_repository.dart';
import 'package:market/features/products/domain/usecases/add_purchase_batch.dart';
import 'package:market/features/products/domain/usecases/get_oldest_purchase_batches.dart';
import 'package:market/features/products/domain/usecases/update_purchase_batch_quantity.dart';

// Internal Transfer imports
import 'package:market/features/products/data/datasources/internal_transfer_database_service.dart';
import 'package:market/features/products/data/repositories/internal_transfer_repository_impl.dart';
import 'package:market/features/products/domain/repositories/internal_transfer_repository.dart';
import 'package:market/features/products/domain/usecases/create_internal_transfer.dart';
import 'package:market/features/products/domain/usecases/get_all_internal_transfers.dart';

// Orders imports
import 'package:market/features/orders/data/datasources/order_database_service.dart';
import 'package:market/features/orders/data/repositories/order_repository_impl.dart';
import 'package:market/features/orders/domain/repositories/order_repository.dart';
import 'package:market/features/orders/domain/usecases/create_order.dart';
import 'package:market/features/orders/domain/usecases/get_all_orders.dart';
import 'package:market/features/orders/domain/usecases/get_order_by_id.dart';
import 'package:market/features/orders/domain/usecases/update_order.dart';
import 'package:market/features/orders/domain/usecases/delete_order.dart';
import 'package:market/features/orders/domain/usecases/generate_order_from_low_stock.dart';

// Notifications imports
import 'package:market/features/notifications/data/datasources/notification_database_service.dart';
import 'package:market/features/notifications/data/repositories/notification_repository_impl.dart';
import 'package:market/features/notifications/domain/repositories/notification_repository.dart';
import 'package:market/features/notifications/domain/usecases/create_notification.dart';
import 'package:market/features/notifications/domain/usecases/get_all_notifications.dart';
import 'package:market/features/notifications/domain/usecases/mark_notification_as_read.dart';
import 'package:market/features/notifications/domain/usecases/mark_notification_action_taken.dart';
import 'package:market/features/notifications/domain/usecases/get_unread_notifications_count.dart';

// Activities imports
import 'package:market/features/activities/data/datasources/activity_database_service.dart';
import 'package:market/features/activities/data/repositories/activity_repository_impl.dart';
import 'package:market/features/activities/domain/repositories/activity_repository.dart';
import 'package:market/features/activities/domain/usecases/get_recent_activities.dart';

// Sales imports
import 'package:market/features/sales/data/datasources/sale_database_service.dart';
import 'package:market/features/sales/data/repositories/sale_repository_impl.dart';
import 'package:market/features/sales/domain/repositories/sale_repository.dart';
import 'package:market/features/sales/domain/usecases/create_sale.dart';
import 'package:market/features/sales/domain/usecases/get_all_sales.dart';
import 'package:market/features/sales/domain/usecases/get_sale_by_id.dart';
import 'package:market/features/sales/domain/usecases/update_sale.dart';
import 'package:market/features/sales/domain/usecases/delete_sale.dart';

// Purchases imports
import 'package:market/features/purchases/data/datasources/purchase_database_service.dart';
import 'package:market/features/purchases/data/repositories/purchase_repository_impl.dart';
import 'package:market/features/purchases/domain/repositories/purchase_repository.dart';
import 'package:market/features/purchases/domain/usecases/create_purchase.dart';
import 'package:market/features/purchases/domain/usecases/get_all_purchases.dart';
import 'package:market/features/purchases/domain/usecases/get_purchase_by_id.dart';
import 'package:market/features/purchases/domain/usecases/update_purchase.dart';
import 'package:market/features/purchases/domain/usecases/delete_purchase.dart';

// Customer Account imports
import 'package:market/features/customers/data/datasources/customer_account_database_service.dart';
import 'package:market/features/customers/data/repositories/customer_account_repository_impl.dart';
import 'package:market/features/customers/domain/repositories/customer_account_repository.dart';
import 'package:market/features/customers/domain/usecases/add_customer_account_entry.dart';
import 'package:market/features/customers/domain/usecases/get_customer_account_statement.dart';

// Inventory Count imports
import 'package:market/features/inventory_count/data/datasources/inventory_count_database_service.dart';
import 'package:market/features/inventory_count/data/repositories/inventory_count_repository_impl.dart';
import 'package:market/features/inventory_count/domain/repositories/inventory_count_repository.dart';
import 'package:market/features/inventory_count/domain/usecases/create_inventory_count.dart';
import 'package:market/features/inventory_count/domain/usecases/get_all_inventory_counts.dart';
import 'package:market/features/inventory_count/domain/usecases/get_inventory_count_by_id.dart';

// Backup Service imports
import 'package:market/core/backup/backup_service.dart';

// *** تم حذف imports التقارير لتبسيط المشروع ***

// Payment Receipts imports
import 'package:market/features/transactions/data/datasources/payment_receipt_database_service.dart';
import 'package:market/features/transactions/data/repositories/payment_receipt_repository_impl.dart';
import 'package:market/features/transactions/domain/repositories/payment_receipt_repository.dart';
import 'package:market/features/transactions/domain/usecases/create_payment_receipt.dart';
import 'package:market/features/transactions/domain/usecases/get_all_payment_receipts.dart';
import 'package:market/features/transactions/domain/usecases/get_payment_receipt_by_id.dart';
import 'package:market/features/transactions/presentation/providers/payment_receipt_provider.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupDependencyInjection() async {
  // Register GlobalKey<NavigatorState> for navigation
  getIt.registerLazySingleton<GlobalKey<NavigatorState>>(
    () => GlobalKey<NavigatorState>(),
  );

  // Register DatabaseService as singleton
  getIt.registerSingleton<DatabaseService>(DatabaseService.instance);

  // Register Data Sources
  getIt.registerLazySingleton<ProductDatabaseService>(
    () => ProductDatabaseService(),
  );
  getIt.registerLazySingleton<CategoryDatabaseService>(
    () => CategoryDatabaseService(),
  );
  getIt.registerLazySingleton<PurchaseBatchDatabaseService>(
    () => PurchaseBatchDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<InternalTransferDatabaseService>(
    () => InternalTransferDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<OrderDatabaseService>(
    () => OrderDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<NotificationDatabaseService>(
    () => NotificationDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<ActivityDatabaseService>(
    () => ActivityDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<SaleDatabaseService>(
    () => SaleDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<PurchaseDatabaseService>(
    () => PurchaseDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<CustomerAccountDatabaseService>(
    () => CustomerAccountDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<InventoryCountDatabaseService>(
    () => InventoryCountDatabaseService(getIt<DatabaseService>()),
  );
  // *** تم حذف AnalyticsDatabaseService لتبسيط المشروع ***
  getIt.registerLazySingleton<PaymentReceiptDatabaseService>(
    () => PaymentReceiptDatabaseService(getIt<DatabaseService>()),
  );

  // Register new DataSources
  getIt.registerLazySingleton<CustomerDatabaseService>(
    () => CustomerDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<SupplierDatabaseService>(
    () => SupplierDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<SupplierAccountDatabaseService>(
    () => SupplierAccountDatabaseService(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<ExpenseDatabaseService>(
    () => ExpenseDatabaseService(getIt<DatabaseService>()),
  );

  // Register Repositories
  getIt.registerLazySingleton<ProductRepository>(
    () => ProductRepositoryImpl(getIt<ProductDatabaseService>()),
  );
  getIt.registerLazySingleton<CategoryRepository>(
    () => CategoryRepositoryImpl(getIt<CategoryDatabaseService>()),
  );
  getIt.registerLazySingleton<PurchaseBatchRepository>(
    () => PurchaseBatchRepositoryImpl(getIt<PurchaseBatchDatabaseService>()),
  );
  getIt.registerLazySingleton<InternalTransferRepository>(
    () => InternalTransferRepositoryImpl(
      getIt<InternalTransferDatabaseService>(),
    ),
  );
  getIt.registerLazySingleton<OrderRepository>(
    () => OrderRepositoryImpl(getIt<OrderDatabaseService>()),
  );
  getIt.registerLazySingleton<NotificationRepository>(
    () => NotificationRepositoryImpl(getIt<NotificationDatabaseService>()),
  );
  getIt.registerLazySingleton<ActivityRepository>(
    () => ActivityRepositoryImpl(getIt<ActivityDatabaseService>()),
  );
  getIt.registerLazySingleton<SaleRepository>(
    () => SaleRepositoryImpl(getIt<SaleDatabaseService>()),
  );
  getIt.registerLazySingleton<PurchaseRepository>(
    () => PurchaseRepositoryImpl(getIt<PurchaseDatabaseService>()),
  );
  getIt.registerLazySingleton<CustomerAccountRepository>(
    () =>
        CustomerAccountRepositoryImpl(getIt<CustomerAccountDatabaseService>()),
  );
  getIt.registerLazySingleton<InventoryCountRepository>(
    () => InventoryCountRepositoryImpl(getIt<InventoryCountDatabaseService>()),
  );
  // *** تم حذف AnalyticsRepository لتبسيط المشروع ***
  getIt.registerLazySingleton<PaymentReceiptRepository>(
    () => PaymentReceiptRepositoryImpl(getIt<PaymentReceiptDatabaseService>()),
  );

  // Register new Repositories
  getIt.registerLazySingleton<CustomerRepository>(
    () => CustomerRepositoryImpl(getIt<CustomerDatabaseService>()),
  );
  getIt.registerLazySingleton<SupplierRepository>(
    () => SupplierRepositoryImpl(getIt<SupplierDatabaseService>()),
  );
  getIt.registerLazySingleton<SupplierAccountRepository>(
    () =>
        SupplierAccountRepositoryImpl(getIt<SupplierAccountDatabaseService>()),
  );
  getIt.registerLazySingleton<ExpenseRepository>(
    () => ExpenseRepositoryImpl(getIt<ExpenseDatabaseService>()),
  );

  // Register Product Use Cases
  getIt.registerLazySingleton<GetAllProductsUseCase>(
    () => GetAllProductsUseCase(getIt<ProductRepository>()),
  );
  getIt.registerLazySingleton<CreateProductUseCase>(
    () => CreateProductUseCase(getIt<ProductRepository>()),
  );
  getIt.registerLazySingleton<UpdateProductUseCase>(
    () => UpdateProductUseCase(getIt<ProductRepository>()),
  );
  getIt.registerLazySingleton<DeleteProductUseCase>(
    () => DeleteProductUseCase(getIt<ProductRepository>()),
  );
  getIt.registerLazySingleton<GetProductByIdUseCase>(
    () => GetProductByIdUseCase(getIt<ProductRepository>()),
  );

  // Register Category Use Cases
  getIt.registerLazySingleton<GetAllCategoriesUseCase>(
    () => GetAllCategoriesUseCase(getIt<CategoryRepository>()),
  );
  getIt.registerLazySingleton<CreateCategoryUseCase>(
    () => CreateCategoryUseCase(getIt<CategoryRepository>()),
  );
  getIt.registerLazySingleton<UpdateCategoryUseCase>(
    () => UpdateCategoryUseCase(getIt<CategoryRepository>()),
  );
  getIt.registerLazySingleton<DeleteCategoryUseCase>(
    () => DeleteCategoryUseCase(getIt<CategoryRepository>()),
  );

  // Register Purchase Batch Use Cases
  getIt.registerLazySingleton<AddPurchaseBatchUseCase>(
    () => AddPurchaseBatchUseCase(getIt<PurchaseBatchRepository>()),
  );
  getIt.registerLazySingleton<GetOldestPurchaseBatchesUseCase>(
    () => GetOldestPurchaseBatchesUseCase(getIt<PurchaseBatchRepository>()),
  );
  getIt.registerLazySingleton<UpdatePurchaseBatchQuantityUseCase>(
    () => UpdatePurchaseBatchQuantityUseCase(getIt<PurchaseBatchRepository>()),
  );

  // Register Internal Transfer Use Cases
  getIt.registerLazySingleton<CreateInternalTransferUseCase>(
    () => CreateInternalTransferUseCase(getIt<InternalTransferRepository>()),
  );
  getIt.registerLazySingleton<GetAllInternalTransfersUseCase>(
    () => GetAllInternalTransfersUseCase(getIt<InternalTransferRepository>()),
  );

  // Register Order Use Cases
  getIt.registerLazySingleton<CreateOrderUseCase>(
    () => CreateOrderUseCase(getIt<OrderRepository>()),
  );
  getIt.registerLazySingleton<GetAllOrdersUseCase>(
    () => GetAllOrdersUseCase(getIt<OrderRepository>()),
  );
  getIt.registerLazySingleton<GetOrderByIdUseCase>(
    () => GetOrderByIdUseCase(getIt<OrderRepository>()),
  );
  getIt.registerLazySingleton<UpdateOrderUseCase>(
    () => UpdateOrderUseCase(getIt<OrderRepository>()),
  );
  getIt.registerLazySingleton<DeleteOrderUseCase>(
    () => DeleteOrderUseCase(getIt<OrderRepository>()),
  );
  getIt.registerLazySingleton<GenerateOrderFromLowStockUseCase>(
    () => GenerateOrderFromLowStockUseCase(getIt<ProductRepository>()),
  );

  // Register Notification Use Cases
  getIt.registerLazySingleton<CreateNotificationUseCase>(
    () => CreateNotificationUseCase(getIt<NotificationRepository>()),
  );
  getIt.registerLazySingleton<GetAllNotificationsUseCase>(
    () => GetAllNotificationsUseCase(getIt<NotificationRepository>()),
  );
  getIt.registerLazySingleton<MarkNotificationAsReadUseCase>(
    () => MarkNotificationAsReadUseCase(getIt<NotificationRepository>()),
  );
  getIt.registerLazySingleton<MarkNotificationActionTakenUseCase>(
    () => MarkNotificationActionTakenUseCase(getIt<NotificationRepository>()),
  );
  getIt.registerLazySingleton<GetUnreadNotificationsCountUseCase>(
    () => GetUnreadNotificationsCountUseCase(getIt<NotificationRepository>()),
  );

  // Register Activity Use Cases
  getIt.registerLazySingleton<GetRecentActivitiesUseCase>(
    () => GetRecentActivitiesUseCase(getIt<ActivityRepository>()),
  );

  // Register Sales Use Cases
  getIt.registerLazySingleton<CreateSaleUseCase>(
    () => CreateSaleUseCase(getIt<SaleRepository>()),
  );
  getIt.registerLazySingleton<GetAllSalesUseCase>(
    () => GetAllSalesUseCase(getIt<SaleRepository>()),
  );
  getIt.registerLazySingleton<GetSaleByIdUseCase>(
    () => GetSaleByIdUseCase(getIt<SaleRepository>()),
  );
  getIt.registerLazySingleton<UpdateSaleUseCase>(
    () => UpdateSaleUseCase(getIt<SaleRepository>()),
  );
  getIt.registerLazySingleton<DeleteSaleUseCase>(
    () => DeleteSaleUseCase(getIt<SaleRepository>()),
  );

  // Register Purchases Use Cases
  getIt.registerLazySingleton<CreatePurchaseUseCase>(
    () => CreatePurchaseUseCase(getIt<PurchaseRepository>()),
  );
  getIt.registerLazySingleton<GetAllPurchasesUseCase>(
    () => GetAllPurchasesUseCase(getIt<PurchaseRepository>()),
  );
  getIt.registerLazySingleton<GetPurchaseByIdUseCase>(
    () => GetPurchaseByIdUseCase(getIt<PurchaseRepository>()),
  );
  getIt.registerLazySingleton<UpdatePurchaseUseCase>(
    () => UpdatePurchaseUseCase(getIt<PurchaseRepository>()),
  );
  getIt.registerLazySingleton<DeletePurchaseUseCase>(
    () => DeletePurchaseUseCase(getIt<PurchaseRepository>()),
  );

  // Register Customer Account Use Cases
  getIt.registerLazySingleton<AddCustomerAccountEntryUseCase>(
    () => AddCustomerAccountEntryUseCase(getIt<CustomerAccountRepository>()),
  );
  getIt.registerLazySingleton<GetCustomerAccountStatementUseCase>(
    () =>
        GetCustomerAccountStatementUseCase(getIt<CustomerAccountRepository>()),
  );

  // Register Inventory Count Use Cases
  getIt.registerLazySingleton<CreateInventoryCountUseCase>(
    () => CreateInventoryCountUseCase(getIt<InventoryCountRepository>()),
  );
  getIt.registerLazySingleton<GetAllInventoryCountsUseCase>(
    () => GetAllInventoryCountsUseCase(getIt<InventoryCountRepository>()),
  );
  getIt.registerLazySingleton<GetInventoryCountByIdUseCase>(
    () => GetInventoryCountByIdUseCase(getIt<InventoryCountRepository>()),
  );

  // *** تم حذف Analytics Use Cases لتبسيط المشروع ***

  // Register Payment Receipt Use Cases
  getIt.registerLazySingleton<CreatePaymentReceiptUseCase>(
    () => CreatePaymentReceiptUseCase(getIt<PaymentReceiptRepository>()),
  );
  getIt.registerLazySingleton<GetAllPaymentReceiptsUseCase>(
    () => GetAllPaymentReceiptsUseCase(getIt<PaymentReceiptRepository>()),
  );
  getIt.registerLazySingleton<GetPaymentReceiptByIdUseCase>(
    () => GetPaymentReceiptByIdUseCase(getIt<PaymentReceiptRepository>()),
  );

  // Register Customer Use Cases
  getIt.registerLazySingleton<CreateCustomer>(
    () => CreateCustomer(getIt<CustomerRepository>()),
  );
  getIt.registerLazySingleton<UpdateCustomer>(
    () => UpdateCustomer(getIt<CustomerRepository>()),
  );
  getIt.registerLazySingleton<GetAllCustomers>(
    () => GetAllCustomers(getIt<CustomerRepository>()),
  );
  getIt.registerLazySingleton<GetCustomerById>(
    () => GetCustomerById(getIt<CustomerRepository>()),
  );
  getIt.registerLazySingleton<DeleteCustomer>(
    () => DeleteCustomer(getIt<CustomerRepository>()),
  );

  // Register Supplier Use Cases
  getIt.registerLazySingleton<CreateSupplier>(
    () => CreateSupplier(getIt<SupplierRepository>()),
  );
  getIt.registerLazySingleton<UpdateSupplier>(
    () => UpdateSupplier(getIt<SupplierRepository>()),
  );
  getIt.registerLazySingleton<GetAllSuppliers>(
    () => GetAllSuppliers(getIt<SupplierRepository>()),
  );
  getIt.registerLazySingleton<GetSupplierById>(
    () => GetSupplierById(getIt<SupplierRepository>()),
  );
  getIt.registerLazySingleton<DeleteSupplier>(
    () => DeleteSupplier(getIt<SupplierRepository>()),
  );

  // Register Supplier Account Use Cases
  getIt.registerLazySingleton<AddSupplierAccountEntryUseCase>(
    () => AddSupplierAccountEntryUseCase(getIt<SupplierAccountRepository>()),
  );
  getIt.registerLazySingleton<GetSupplierAccountStatementUseCase>(
    () =>
        GetSupplierAccountStatementUseCase(getIt<SupplierAccountRepository>()),
  );

  // Register Expense Use Cases
  getIt.registerLazySingleton<CreateExpense>(
    () => CreateExpense(getIt<ExpenseRepository>()),
  );
  getIt.registerLazySingleton<UpdateExpense>(
    () => UpdateExpense(getIt<ExpenseRepository>()),
  );
  getIt.registerLazySingleton<GetAllExpenses>(
    () => GetAllExpenses(getIt<ExpenseRepository>()),
  );
  getIt.registerLazySingleton<GetExpenseById>(
    () => GetExpenseById(getIt<ExpenseRepository>()),
  );
  getIt.registerLazySingleton<DeleteExpense>(
    () => DeleteExpense(getIt<ExpenseRepository>()),
  );

  // Register Services
  getIt.registerLazySingleton<BackupService>(() => BackupService.instance);

  // Register Providers (in dependency order)

  // Register simple Providers first (no dependencies on other providers)
  getIt.registerLazySingleton<ProductProvider>(
    () => ProductProvider(
      getIt<GetAllProductsUseCase>(),
      getIt<CreateProductUseCase>(),
      getIt<UpdateProductUseCase>(),
      getIt<DeleteProductUseCase>(),
      getIt<GetProductByIdUseCase>(),
      getIt<AddPurchaseBatchUseCase>(),
      getIt<GetOldestPurchaseBatchesUseCase>(),
      getIt<UpdatePurchaseBatchQuantityUseCase>(),
      getIt<CreateNotificationUseCase>(), // *** جديد ***
    ),
  );

  getIt.registerLazySingleton<CategoryProvider>(() => CategoryProvider());

  getIt.registerLazySingleton<ActivityProvider>(() => ActivityProvider());

  getIt.registerLazySingleton<NotificationProvider>(
    () => NotificationProvider(),
  );

  // Register Customer and Supplier Providers
  getIt.registerLazySingleton<CustomerProvider>(
    () => CustomerProvider(
      getIt<CreateCustomer>(),
      getIt<UpdateCustomer>(),
      getIt<GetAllCustomers>(),
      getIt<GetCustomerById>(),
      getIt<DeleteCustomer>(),
      getIt<AddCustomerAccountEntryUseCase>(),
      getIt<GetCustomerAccountStatementUseCase>(),
    ),
  );

  getIt.registerLazySingleton<SupplierProvider>(
    () => SupplierProvider(
      getIt<CreateSupplier>(),
      getIt<UpdateSupplier>(),
      getIt<GetAllSuppliers>(),
      getIt<GetSupplierById>(),
      getIt<DeleteSupplier>(),
      getIt<AddSupplierAccountEntryUseCase>(),
      getIt<GetSupplierAccountStatementUseCase>(),
    ),
  );

  // Register ExpenseProvider
  getIt.registerLazySingleton<ExpenseProvider>(
    () => ExpenseProvider(
      getIt<CreateExpense>(),
      getIt<UpdateExpense>(),
      getIt<GetAllExpenses>(),
      getIt<GetExpenseById>(),
      getIt<DeleteExpense>(),
      getIt<ExpenseRepository>(),
    ),
  );

  // Register PaymentReceiptProvider
  getIt.registerLazySingleton<PaymentReceiptProvider>(
    () => PaymentReceiptProvider(
      createPaymentReceiptUseCase: getIt<CreatePaymentReceiptUseCase>(),
      getAllPaymentReceiptsUseCase: getIt<GetAllPaymentReceiptsUseCase>(),
      getPaymentReceiptByIdUseCase: getIt<GetPaymentReceiptByIdUseCase>(),
      customerProvider: getIt<CustomerProvider>(),
      supplierProvider: getIt<SupplierProvider>(),
    ),
  );

  // Register other complex Providers (temporarily without dependencies)
  getIt.registerLazySingleton<InternalTransferProvider>(
    () => InternalTransferProvider(
      getIt<CreateInternalTransferUseCase>(),
      getIt<GetAllInternalTransfersUseCase>(),
      getIt<GetProductByIdUseCase>(),
      getIt<ProductProvider>(),
    ),
  );
  getIt.registerLazySingleton<InventoryCountProvider>(
    () => InventoryCountProvider(),
  );
  getIt.registerLazySingleton<SaleProvider>(() => SaleProvider());
  getIt.registerLazySingleton<PurchaseProvider>(() => PurchaseProvider());
  getIt.registerLazySingleton<OrderProvider>(() => OrderProvider());
  // *** تم حذف AnalyticsProvider لتبسيط المشروع ***
}

Future<void> configureDependencies() async {
  await setupDependencyInjection();
}
