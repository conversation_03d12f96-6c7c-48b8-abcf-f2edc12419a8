import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../features/notifications/presentation/providers/notification_provider.dart';

class ConditionalBottomNav extends StatelessWidget {
  const ConditionalBottomNav({super.key});

  // Routes that should show bottom navigation
  static const List<String> _bottomNavRoutes = [
    '/',
    '/products',
    '/customers',
    '/sales',
    '/reports',
    '/notifications',
  ];

  @override
  Widget build(BuildContext context) {
    final currentRoute = GoRouterState.of(context).fullPath ?? '/';

    // Only show bottom nav for specific routes
    if (!_bottomNavRoutes.contains(currentRoute)) {
      return const SizedBox.shrink();
    }

    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        return BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _getCurrentIndex(currentRoute),
          onTap: (index) => _onTap(context, index),
          selectedItemColor: Theme.of(context).colorScheme.primary,
          unselectedItemColor: Theme.of(
            context,
          ).colorScheme.onSurface.withValues(alpha: 0.6),
          items: [
            const BottomNavigationBarItem(
              icon: Icon(Icons.dashboard),
              label: 'الرئيسية',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.inventory),
              label: 'المنتجات',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.people),
              label: 'العملاء',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.point_of_sale),
              label: 'المبيعات',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.analytics),
              label: 'التقارير',
            ),
            BottomNavigationBarItem(
              icon: Stack(
                children: [
                  const Icon(Icons.notifications),
                  if (notificationProvider.unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${notificationProvider.unreadCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              label: 'التنبيهات',
            ),
          ],
        );
      },
    );
  }

  int _getCurrentIndex(String currentRoute) {
    switch (currentRoute) {
      case '/':
        return 0;
      case '/products':
        return 1;
      case '/customers':
        return 2;
      case '/sales':
        return 3;
      case '/reports':
        return 4;
      case '/notifications':
        return 5;
      default:
        return 0;
    }
  }

  void _onTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/sales');
        break;
      case 4:
        context.go('/reports');
        break;
      case 5:
        context.go('/notifications');
        break;
    }
  }
}
