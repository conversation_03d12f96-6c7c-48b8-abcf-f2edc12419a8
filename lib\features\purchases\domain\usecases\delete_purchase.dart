import '../repositories/purchase_repository.dart';

class DeletePurchaseUseCase {
  final PurchaseRepository _repository;

  DeletePurchaseUseCase(this._repository);

  Future<void> call(int id) async {
    if (id <= 0) {
      throw Exception('Purchase ID must be greater than 0');
    }

    try {
      await _repository.deletePurchase(id);
    } catch (e) {
      throw Exception('Failed to delete purchase: $e');
    }
  }
}
