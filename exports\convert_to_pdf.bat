@echo off
echo 🚀 Market App Documentation PDF Converter
echo ==========================================

echo.
echo 📄 تحويل HTML إلى PDF...

REM التحقق من وجود ملف HTML
if not exist "Market_App_Documentation.html" (
    echo ❌ ملف HTML غير موجود
    pause
    exit /b 1
)

REM محاولة التحويل باستخدام Chrome
echo 🌐 محاولة التحويل باستخدام Chrome...
"C:\Program Files\Google\Chrome\Application\chrome.exe" --headless --disable-gpu --print-to-pdf="Market_App_Documentation.pdf" --print-to-pdf-no-header --run-all-compositor-stages-before-draw --virtual-time-budget=10000 "file://%CD%\Market_App_Documentation.html"

if exist "Market_App_Documentation.pdf" (
    echo ✅ تم إنشاء PDF بنجاح!
    echo 📁 الملف: Market_App_Documentation.pdf
) else (
    echo ❌ فشل في التحويل
    echo 💡 تأكد من تثبيت Google Chrome
)

echo.
pause
