import '../../data/models/order_model.dart';

class Order {
  final int? id;
  final DateTime orderDate;
  final double totalEstimatedCost;
  final String? notes;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Order({
    this.id,
    required this.orderDate,
    required this.totalEstimatedCost,
    this.notes,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  // Create Order from OrderModel
  factory Order.fromModel(OrderModel model) {
    return Order(
      id: model.id,
      orderDate: model.orderDate,
      totalEstimatedCost: model.totalEstimatedCost,
      notes: model.notes,
      status: model.status,
    );
  }

  // Business logic methods
  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'قيد الانتظار';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  // Copy with method for updates
  Order copyWith({
    int? id,
    DateTime? orderDate,
    double? totalEstimatedCost,
    String? notes,
    String? status,
  }) {
    return Order(
      id: id ?? this.id,
      orderDate: orderDate ?? this.orderDate,
      totalEstimatedCost: totalEstimatedCost ?? this.totalEstimatedCost,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'Order(id: $id, orderDate: $orderDate, '
        'totalEstimatedCost: $totalEstimatedCost, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order &&
        other.id == id &&
        other.orderDate == orderDate &&
        other.totalEstimatedCost == totalEstimatedCost &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderDate.hashCode ^
        totalEstimatedCost.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
