import '../entities/purchase.dart';
import '../entities/purchase_item.dart';

abstract class PurchaseRepository {
  /// Create a new purchase with its items
  Future<int> createPurchase(Purchase purchase, List<PurchaseItem> items);

  /// Get all purchases
  Future<List<Purchase>> getAllPurchases();

  /// Get purchase by ID
  Future<Purchase?> getPurchaseById(int id);

  /// Get purchase items by purchase ID
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId);

  /// Update purchase
  Future<void> updatePurchase(Purchase purchase);

  /// Update purchase with items
  Future<void> updatePurchaseWithItems(
    Purchase purchase,
    List<PurchaseItem> items,
  );

  /// Delete purchase
  Future<void> deletePurchase(int id);

  /// Get purchases by supplier
  Future<List<Purchase>> getPurchasesBySupplier(int supplierId);

  /// Get purchases by status
  Future<List<Purchase>> getPurchasesByStatus(String status);

  /// Get purchases by payment method
  Future<List<Purchase>> getPurchasesByPaymentMethod(String paymentMethod);

  /// Get total purchases amount by supplier
  Future<double> getTotalPurchasesBySupplier(int supplierId);

  /// Get total payments made to supplier
  Future<double> getTotalPaymentsMadeToSupplier(int supplierId);

  /// Get purchase statistics
  Future<Map<String, dynamic>> getPurchaseStatistics();
}
