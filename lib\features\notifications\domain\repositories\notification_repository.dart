import '../entities/app_notification.dart';

abstract class NotificationRepository {
  /// Create a new notification
  Future<int> createNotification(AppNotification notification);

  /// Get all notifications
  Future<List<AppNotification>> getNotifications({
    int limit = 50,
    int offset = 0,
  });

  /// Get unread notifications count
  Future<int> getUnreadNotificationsCount();

  /// Mark notification as read
  Future<void> markNotificationAsRead(int id);

  /// Mark notification action as taken
  Future<void> markNotificationActionTaken(int id);

  /// Get notifications by type
  Future<List<AppNotification>> getNotificationsByType(String type);

  /// Get notifications for a specific entity
  Future<List<AppNotification>> getNotificationsForEntity(
    String entityType,
    int entityId,
  );

  /// Delete notification
  Future<void> deleteNotification(int id);

  /// Delete old notifications (older than specified days)
  Future<void> deleteOldNotifications(int daysOld);

  /// Mark all notifications as read
  Future<void> markAllNotificationsAsRead();

  /// Get notification statistics
  Future<Map<String, dynamic>> getNotificationStatistics();
}
