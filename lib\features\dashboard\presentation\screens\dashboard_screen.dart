import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../../../activities/presentation/providers/activity_provider.dart';
import '../../../activities/domain/entities/activity.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityProvider>().fetchActivities();
    });

    // Add scroll listener for infinite scrolling
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more when near bottom
      context.read<ActivityProvider>().loadMoreActivities();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: 'الواجهة الرئيسية',
      child: RefreshIndicator(
        onRefresh: () => context.read<ActivityProvider>().refreshActivities(),
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Quick Action Buttons
              _buildQuickActionButtons(context),

              const SizedBox(height: 24),

              // Activities Section
              Text(
                'آخر النشاطات',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 16),

              // Activities List
              _buildActivitiesList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionButtons(BuildContext context) {
    final quickActions = [
      {
        'title': 'بيع',
        'icon': Icons.point_of_sale,
        'route': '/sales/new',
        'color': Colors.green,
      },
      {
        'title': 'توريد',
        'icon': Icons.shopping_cart,
        'route': '/purchases/new',
        'color': Colors.blue,
      },
      {
        'title': 'تحويل داخلي',
        'icon': Icons.swap_horiz,
        'route': '/transfers',
        'color': Colors.orange,
      },
      {
        'title': 'مصروف',
        'icon': Icons.money_off,
        'route': '/expenses/new',
        'color': Colors.red,
      },
      {
        'title': 'تسجيل طلبية',
        'icon': Icons.add_shopping_cart,
        'route': '/orders/new',
        'color': Colors.purple,
      },
      {
        'title': 'جرد المخزن',
        'icon': Icons.inventory,
        'route': '/inventory_count/new',
        'color': Colors.teal,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: quickActions.length,
      itemBuilder: (context, index) {
        final action = quickActions[index];
        return _buildQuickActionCard(
          context,
          title: action['title'] as String,
          icon: action['icon'] as IconData,
          route: action['route'] as String,
          color: action['color'] as Color,
        );
      },
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required String route,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => context.go(route),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivitiesList() {
    return Consumer<ActivityProvider>(
      builder: (context, activityProvider, child) {
        if (activityProvider.isLoading && activityProvider.activities.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (activityProvider.errorMessage != null) {
          return Center(
            child: Column(
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  activityProvider.errorMessage!,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => activityProvider.refreshActivities(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (activityProvider.activities.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Column(
                children: [
                  Icon(Icons.history, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد أنشطة حتى الآن',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'ستظهر الأنشطة هنا عند إجراء عمليات',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          children: [
            // Activities List
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activityProvider.activities.length,
              itemBuilder: (context, index) {
                final activity = activityProvider.activities[index];
                return _buildActivityItem(activity);
              },
            ),

            // Load More Indicator
            if (activityProvider.isLoadingMore)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),

            // No More Data Indicator
            if (!activityProvider.hasMore &&
                activityProvider.activities.isNotEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'لا توجد أنشطة أخرى',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildActivityItem(Activity activity) {
    final itemColor = _getActivityColor(activity.type);
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2, // الحفاظ على Elevation
      // color: itemColor.withValues(alpha: 0.08), // حذف تلوين البطاقة
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        leading: CircleAvatar(
          backgroundColor: itemColor.withValues(
            alpha: 0.15,
          ), // لون أغمق قليلاً للدائرة
          radius: 24, // حجم أكبر للدائرة
          child: Icon(
            _getActivityIcon(activity.type),
            color: itemColor,
            size: 24, // حجم أكبر للأيقونة
          ),
        ),
        title: Text(
          activity
              .description, // هذا الوصف سيأتي الآن من الـ SQL ويجب أن يكون مفصلاً
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 15,
            color: Colors.black87,
          ),
          maxLines: 2, // لضمان عدم تجاوز السطرين للوصف الطويل
          overflow: TextOverflow.ellipsis, // لإظهار "..." إذا كان الوصف طويلاً
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${activity.typeDisplayName}${activity.paymentMethod != null && activity.paymentMethod!.isNotEmpty ? ' • ${activity.paymentMethod}' : ''} • ${DateFormat('yyyy-MM-dd HH:mm').format(activity.date)}',
              style: const TextStyle(fontSize: 13, color: Colors.grey),
            ),
            if (activity.hasAmount)
              Text(
                activity.formattedAmount,
                style: TextStyle(
                  fontSize: 13,
                  color: itemColor.withValues(alpha: 0.9),
                  fontWeight: FontWeight.bold,
                ),
              ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: activity.isModified
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                activity.statusText,
                style: TextStyle(
                  fontSize: 10,
                  color: activity.isModified ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: activity.canEdit
            ? Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400])
            : null,
        onTap: activity.targetRoute != null
            ? () => context.go(activity.targetRoute!)
            : null,
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'sale':
        return Icons.point_of_sale;
      case 'purchase':
        return Icons.shopping_cart;
      case 'expense':
        return Icons.money_off;
      case 'transfer':
        return Icons.swap_horiz;
      case 'payment':
        return Icons.payment;
      case 'payment_receipt': // إضافة هذه الحالة
        return Icons.receipt_long;
      case 'retail_debt':
        return Icons.credit_card;
      case 'inventory_count':
        return Icons.inventory;
      case 'order':
        return Icons.add_shopping_cart;
      case 'notification':
        return Icons.notifications;
      default:
        return Icons.history;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'sale':
        return Colors.green;
      case 'purchase':
        return Colors.blue;
      case 'expense':
        return Colors.red;
      case 'transfer':
        return Colors.orange;
      case 'payment':
        return Colors.purple;
      case 'payment_receipt': // إضافة هذه الحالة
        return Colors.purple; // لون معبر للسندات المالية
      case 'retail_debt':
        return Colors.indigo;
      case 'inventory_count':
        return Colors.teal;
      case 'order':
        return Colors.purple;
      case 'notification':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}
