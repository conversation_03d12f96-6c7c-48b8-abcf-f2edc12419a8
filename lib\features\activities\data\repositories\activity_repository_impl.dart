import '../../domain/entities/activity.dart';
import '../../domain/repositories/activity_repository.dart';
import '../datasources/activity_database_service.dart';

class ActivityRepositoryImpl implements ActivityRepository {
  final ActivityDatabaseService _databaseService;

  ActivityRepositoryImpl(this._databaseService);

  @override
  Future<List<Activity>> getActivities({int limit = 10, int offset = 0}) async {
    try {
      final activityModels = await _databaseService.getActivities(
        limit: limit,
        offset: offset,
      );
      return activityModels.map((model) => Activity.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get activities: $e');
    }
  }

  @override
  Future<int> getActivitiesCount() async {
    try {
      return await _databaseService.getActivitiesCount();
    } catch (e) {
      throw Exception('Failed to get activities count: $e');
    }
  }

  @override
  Future<List<Activity>> getActivitiesByType(
    String type, {
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      final activityModels = await _databaseService.getActivitiesByType(
        type,
        limit: limit,
        offset: offset,
      );
      return activityModels.map((model) => Activity.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get activities by type: $e');
    }
  }

  @override
  Future<List<Activity>> getRecentActivities({int limit = 20}) async {
    try {
      final activityModels = await _databaseService.getRecentActivities(
        limit: limit,
      );
      return activityModels.map((model) => Activity.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get recent activities: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getActivityStatistics() async {
    try {
      return await _databaseService.getActivityStatistics();
    } catch (e) {
      throw Exception('Failed to get activity statistics: $e');
    }
  }
}
