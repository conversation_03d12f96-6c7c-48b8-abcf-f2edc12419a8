import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/supplier_model.dart';

class SupplierDatabaseService {
  final DatabaseService _databaseService;

  SupplierDatabaseService(this._databaseService);

  Future<List<SupplierModel>> getAllSuppliers() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return SupplierModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get suppliers: $e');
    }
  }

  Future<SupplierModel?> getSupplierById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return SupplierModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get supplier by id: $e');
    }
  }

  Future<int> createSupplier(SupplierModel supplier) async {
    try {
      final db = await _databaseService.database;
      final supplierMap = supplier.toMap();
      supplierMap.remove('id'); // Remove id for auto-increment

      return await db.insert(
        'suppliers',
        supplierMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to create supplier: $e');
    }
  }

  Future<void> updateSupplier(SupplierModel supplier) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'suppliers',
        supplier.toMap(),
        where: 'id = ?',
        whereArgs: [supplier.id],
      );
    } catch (e) {
      throw Exception('Failed to update supplier: $e');
    }
  }

  Future<void> deleteSupplier(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete('suppliers', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('Failed to delete supplier: $e');
    }
  }

  Future<List<SupplierModel>> searchSuppliers(String query) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where:
            'name LIKE ? OR phone LIKE ? OR email LIKE ? OR contact_person LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return SupplierModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to search suppliers: $e');
    }
  }
}
