import '../entities/customer.dart';
import '../repositories/customer_repository.dart';

class CreateCustomer {
  final CustomerRepository _repository;

  CreateCustomer(this._repository);

  Future<int> call(Customer customer) async {
    // Validation
    if (customer.name.trim().isEmpty) {
      throw Exception('Customer name cannot be empty');
    }

    if (customer.creditLimit < 0) {
      throw Exception('Credit limit cannot be negative');
    }

    // تم حذف فحص currentBalance لأنه لم يعد موجوداً في Customer entity

    // Validate email format if provided
    if (customer.email != null && customer.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(customer.email!)) {
        throw Exception('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (customer.phone != null && customer.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^[\d\s\-\+\(\)]+$');
      if (!phoneRegex.hasMatch(customer.phone!)) {
        throw Exception('Invalid phone format');
      }
    }

    try {
      return await _repository.createCustomer(customer);
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }
}
