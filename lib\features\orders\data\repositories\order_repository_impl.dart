import '../../domain/entities/order.dart';
import '../../domain/entities/order_item.dart';
import '../../domain/repositories/order_repository.dart';
import '../datasources/order_database_service.dart';
import '../models/order_model.dart';
import '../models/order_item_model.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderDatabaseService _databaseService;

  OrderRepositoryImpl(this._databaseService);

  @override
  Future<int> createOrder(Order order, List<OrderItem> items) async {
    try {
      final orderModel = OrderModel(
        id: order.id,
        orderDate: order.orderDate,
        totalEstimatedCost: order.totalEstimatedCost,
        notes: order.notes,
        status: order.status,
      );

      final itemModels = items
          .map(
            (item) => OrderItemModel(
              id: item.id,
              orderId: item.orderId,
              productId: item.productId,
              quantity: item.quantity,
              estimatedUnitPrice: item.estimatedUnitPrice,
            ),
          )
          .toList();

      return await _databaseService.createOrder(orderModel, itemModels);
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  @override
  Future<List<Order>> getAllOrders() async {
    try {
      final orderModels = await _databaseService.getAllOrders();
      return orderModels.map((model) => Order.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get all orders: $e');
    }
  }

  @override
  Future<Order?> getOrderById(int id) async {
    try {
      final orderModel = await _databaseService.getOrderById(id);
      return orderModel != null ? Order.fromModel(orderModel) : null;
    } catch (e) {
      throw Exception('Failed to get order by id: $e');
    }
  }

  @override
  Future<List<OrderItem>> getOrderItems(int orderId) async {
    try {
      final itemModels = await _databaseService.getOrderItems(orderId);
      return itemModels.map((model) => OrderItem.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get order items: $e');
    }
  }

  @override
  Future<void> updateOrder(Order order) async {
    try {
      final orderModel = OrderModel(
        id: order.id,
        orderDate: order.orderDate,
        totalEstimatedCost: order.totalEstimatedCost,
        notes: order.notes,
        status: order.status,
      );

      await _databaseService.updateOrder(orderModel);
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }

  @override
  Future<void> updateOrderWithItems(Order order, List<OrderItem> items) async {
    try {
      final orderModel = OrderModel(
        id: order.id,
        orderDate: order.orderDate,
        totalEstimatedCost: order.totalEstimatedCost,
        notes: order.notes,
        status: order.status,
      );

      final itemModels = items
          .map(
            (item) => OrderItemModel(
              id: item.id,
              orderId: item.orderId,
              productId: item.productId,
              quantity: item.quantity,
              estimatedUnitPrice: item.estimatedUnitPrice,
            ),
          )
          .toList();

      await _databaseService.updateOrderWithItems(orderModel, itemModels);
    } catch (e) {
      throw Exception('Failed to update order with items: $e');
    }
  }

  @override
  Future<void> deleteOrder(int id) async {
    try {
      await _databaseService.deleteOrder(id);
    } catch (e) {
      throw Exception('Failed to delete order: $e');
    }
  }

  @override
  Future<List<Order>> getOrdersByStatus(String status) async {
    try {
      final orderModels = await _databaseService.getOrdersByStatus(status);
      return orderModels.map((model) => Order.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get orders by status: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getOrderStatistics() async {
    try {
      return await _databaseService.getOrderStatistics();
    } catch (e) {
      throw Exception('Failed to get order statistics: $e');
    }
  }
}
