import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/customer.dart';
import '../../domain/entities/customer_account_statement_item.dart';
import '../providers/customer_provider.dart';
import '../../../../shared_widgets/wrappers.dart';
import '../../../../core/widgets/date_range_picker_widget.dart';
import '../../../../core/services/pdf_service.dart';

class CustomerAccountStatementDetailsScreen extends StatefulWidget {
  final int customerId;

  const CustomerAccountStatementDetailsScreen({
    super.key,
    required this.customerId,
  });

  @override
  State<CustomerAccountStatementDetailsScreen> createState() =>
      _CustomerAccountStatementDetailsScreenState();
}

class _CustomerAccountStatementDetailsScreenState
    extends State<CustomerAccountStatementDetailsScreen> {
  Customer? _customer;
  bool _isLoadingCustomer = true;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _showDatePicker = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final provider = context.read<CustomerProvider>();

    // تحميل بيانات العميل
    setState(() => _isLoadingCustomer = true);
    try {
      _customer = await provider.getCustomerById(widget.customerId);
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => _isLoadingCustomer = false);
      }
    }

    // تحميل كشف الحساب مع المدة الزمنية
    await provider.fetchAccountStatement(
      widget.customerId,
      fromDate: _startDate,
      toDate: _endDate,
    );
  }

  void _onDateRangeChanged(DateTime? startDate, DateTime? endDate) {
    setState(() {
      _startDate = startDate;
      _endDate = endDate;
    });
    _loadData(); // إعادة تحميل البيانات عند تغيير المدة
  }

  Future<void> _exportToPdf() async {
    try {
      final provider = context.read<CustomerProvider>();
      if (provider.statementItems.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('لا توجد بيانات للتصدير')));
        return;
      }

      if (_customer == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('بيانات العميل غير متوفرة')),
        );
        return;
      }

      // عرض خيارات PDF
      if (mounted) {
        await _showPdfOptions(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('فشل في التصدير: $e')));
      }
    }
  }

  Future<void> _showPdfOptions(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'خيارات تصدير كشف الحساب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.print, color: Colors.blue),
              title: const Text('طباعة'),
              onTap: () {
                Navigator.pop(context);
                _generateAndProcessStatementPDF('print');
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: Colors.green),
              title: const Text('مشاركة'),
              onTap: () {
                Navigator.pop(context);
                _generateAndProcessStatementPDF('share');
              },
            ),
            ListTile(
              leading: const Icon(Icons.save, color: Colors.orange),
              title: const Text('حفظ في الجهاز'),
              onTap: () {
                Navigator.pop(context);
                _generateAndProcessStatementPDF('save');
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateAndProcessStatementPDF(String action) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final provider = context.read<CustomerProvider>();

      // إنشاء PDF لكشف الحساب
      final pdfData = await PDFService.generateCustomerStatementPDF(
        customer: _customer!,
        statementItems: provider.statementItems,
        fromDate: _startDate,
        toDate: _endDate,
        openingBalance: 0.0, // سيتم تحسينه لاحقاً
      );

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      // تنفيذ الإجراء المطلوب
      switch (action) {
        case 'print':
          await PDFService.printPDF(pdfData, 'كشف حساب ${_customer!.name}');
          break;
        case 'share':
          await PDFService.sharePDF(pdfData, 'كشف_حساب_${_customer!.name}');
          break;
        case 'save':
          final path = await PDFService.savePDF(
            pdfData,
            'كشف_حساب_${_customer!.name}',
          );
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text('تم حفظ الملف في: $path')));
          }
          break;
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في معالجة PDF: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _customer?.name ?? 'كشف حساب العميل',
      actions: [
        // زر تبديل عرض اختيار المدة
        IconButton(
          onPressed: () {
            setState(() {
              _showDatePicker = !_showDatePicker;
            });
          },
          icon: Icon(_showDatePicker ? Icons.expand_less : Icons.expand_more),
          tooltip: 'تحديد المدة الزمنية',
        ),
        // زر التصدير
        IconButton(
          onPressed: _exportToPdf,
          icon: const Icon(Icons.picture_as_pdf),
          tooltip: 'تصدير PDF',
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // الانتقال إلى شاشة إضافة سند مع تمرير معلومات العميل
          final result = await context.push(
            '/transactions/receipts/new',
            extra: {
              'relatedEntityType': 'customer',
              'relatedEntityId': widget.customerId,
            },
          );

          // إعادة تحميل البيانات إذا تم إنشاء سند بنجاح
          if (result == true && mounted) {
            await _loadData();
          }
        },
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: Consumer<CustomerProvider>(
        builder: (context, provider, child) {
          if (_isLoadingCustomer || provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.statementItems.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد معاملات لهذا العميل حتى الآن',
                    style: TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // اختيار المدة الزمنية (قابل للطي)
              if (_showDatePicker)
                DateRangePickerWidget(
                  initialStartDate: _startDate,
                  initialEndDate: _endDate,
                  onDateRangeChanged: _onDateRangeChanged,
                  title: 'تحديد مدة كشف الحساب',
                ),

              // رأس الكشف (Header)
              _buildAccountSummaryCard(provider.statementItems),
              const SizedBox(height: 16),

              // رأس الجدول
              _buildTableHeader(),
              const SizedBox(height: 8),

              // قائمة المعاملات
              Expanded(child: _buildTransactionsList(provider.statementItems)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAccountSummaryCard(List<CustomerAccountStatementItem> items) {
    // حساب الإجماليات
    double totalDebits = 0.0;
    double totalCredits = 0.0;
    double finalBalance = 0.0;

    for (final item in items) {
      totalDebits += item.debit;
      totalCredits += item.credit;
    }

    if (items.isNotEmpty) {
      finalBalance = items.first.runningBalance; // الأحدث أولاً
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم العميل
            Text(
              _customer?.name ?? 'عميل غير معروف',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // الملخص المالي
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الديون',
                    totalDebits,
                    Colors.blue,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الدفعات',
                    totalCredits,
                    Colors.green,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // الرصيد النهائي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: finalBalance > 0
                    ? Colors.red.shade50
                    : finalBalance < 0
                    ? Colors.green.shade50
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: finalBalance > 0
                      ? Colors.red.shade200
                      : finalBalance < 0
                      ? Colors.green.shade200
                      : Colors.grey.shade200,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الرصيد النهائي:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${finalBalance.toStringAsFixed(2)} ر.ي',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: finalBalance > 0
                          ? Colors.red.shade700
                          : finalBalance < 0
                          ? Colors.green.shade700
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ر.ي',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'التاريخ',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'البيان',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'مدين',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'دائن',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'الرصيد',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(List<CustomerAccountStatementItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildTransactionCard(item);
      },
    );
  }

  Widget _buildTransactionCard(CustomerAccountStatementItem item) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            // التاريخ
            Expanded(
              flex: 2,
              child: Text(
                dateFormat.format(item.transaction.transactionDate),
                style: const TextStyle(fontSize: 12),
              ),
            ),

            // البيان
            Expanded(
              flex: 3,
              child: Text(
                item.transaction.description ?? 'بدون وصف',
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // مدين
            Expanded(
              flex: 2,
              child: Text(
                item.debit > 0 ? item.debit.toStringAsFixed(2) : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // دائن
            Expanded(
              flex: 2,
              child: Text(
                item.credit > 0 ? item.credit.toStringAsFixed(2) : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // الرصيد
            Expanded(
              flex: 2,
              child: Text(
                item.runningBalance.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: item.runningBalance > 0
                      ? Colors.red.shade700
                      : item.runningBalance < 0
                      ? Colors.green.shade700
                      : Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
