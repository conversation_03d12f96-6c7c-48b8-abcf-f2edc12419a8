import '../entities/internal_transfer.dart';
import '../repositories/internal_transfer_repository.dart';

class GetAllInternalTransfersUseCase {
  final InternalTransferRepository _repository;

  GetAllInternalTransfersUseCase(this._repository);

  Future<List<InternalTransfer>> call() async {
    try {
      return await _repository.getTransfers();
    } catch (e) {
      throw Exception('Failed to get all internal transfers: $e');
    }
  }
}
