import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/custom_app_bar.dart';
import 'package:market/shared_widgets/modern_drawer.dart';
import 'package:market/shared_widgets/conditional_bottom_nav.dart';

class MainScreenWrapper extends StatelessWidget {
  final String title;
  final Widget child;
  final bool needsDrawer;
  final bool showBottomNav;
  final Widget? floatingActionButton;
  final PreferredSizeWidget? customAppBar;

  const MainScreenWrapper({
    super.key,
    required this.title,
    required this.child,
    this.needsDrawer = true,
    this.showBottomNav = true,
    this.floatingActionButton,
    this.customAppBar,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final currentRoute = GoRouterState.of(context).fullPath ?? '/';

        // If we're on the main dashboard, show exit confirmation
        if (currentRoute == '/') {
          final shouldExit = await _showExitConfirmation(context);
          if (shouldExit && context.mounted) {
            SystemNavigator.pop();
          }
          return;
        }

        // For other main screens, go back to dashboard
        if (context.mounted) {
          context.go('/');
        }
      },
      child: Scaffold(
        appBar:
            customAppBar ?? CustomAppBar(title: title, showBackButton: false),
        drawer: needsDrawer ? const ModernDrawer() : null,
        body: child,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: showBottomNav
            ? const ConditionalBottomNav()
            : null,
      ),
    );
  }

  Future<bool> _showExitConfirmation(BuildContext context) async {
    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        title: const Text(
          'تأكيد الخروج',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'هل تريد الخروج من التطبيق؟',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(false),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(true),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('خروج', style: TextStyle(fontSize: 16)),
                ),
              ),
            ],
          ),
        ],
      ),
    );

    return result ?? false;
  }
}

class SecondaryScreenWrapper extends StatelessWidget {
  final String title;
  final Widget child;
  final VoidCallback? onBackPressed;
  final Widget? floatingActionButton;
  final List<Widget>? actions;

  const SecondaryScreenWrapper({
    super.key,
    required this.title,
    required this.child,
    this.onBackPressed,
    this.floatingActionButton,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // تحسين منطق التنقل
        if (onBackPressed != null) {
          onBackPressed!();
        } else if (GoRouter.of(context).canPop()) {
          // إذا كان بإمكان العودة إلى الشاشة السابقة، قم بذلك
          GoRouter.of(context).pop();
        } else if (context.mounted) {
          // إذا لم يكن بإمكان العودة، انتقل إلى الشاشة الرئيسية
          context.go('/');
        }
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: title,
          showBackButton: true,
          actions: actions,
          onBackPressed:
              onBackPressed ??
              () {
                if (GoRouter.of(context).canPop()) {
                  GoRouter.of(context).pop();
                } else if (context.mounted) {
                  context.go('/');
                }
              },
        ),
        body: child,
        floatingActionButton: floatingActionButton,
      ),
    );
  }
}
