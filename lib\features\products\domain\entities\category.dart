import 'package:market/features/products/data/models/category_model.dart';

class Category {
  final int? id;
  final String name;

  const Category({this.id, required this.name});

  // Create Category from CategoryModel
  factory Category.fromModel(CategoryModel model) {
    return Category(id: model.id, name: model.name);
  }

  // Copy with method for updates
  Category copyWith({int? id, String? name}) {
    return Category(id: id ?? this.id, name: name ?? this.name);
  }

  @override
  String toString() {
    return 'Category(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
