import '../entities/supplier.dart';
import '../repositories/supplier_repository.dart';

class GetSupplierById {
  final SupplierRepository _repository;

  GetSupplierById(this._repository);

  Future<Supplier?> call(int id) async {
    if (id <= 0) {
      throw Exception('Invalid supplier ID');
    }

    try {
      return await _repository.getSupplierById(id);
    } catch (e) {
      throw Exception('Failed to get supplier by id: $e');
    }
  }
}
