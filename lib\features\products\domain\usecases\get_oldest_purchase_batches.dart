import '../entities/purchase_batch.dart';
import '../repositories/purchase_batch_repository.dart';

class GetOldestPurchaseBatchesUseCase {
  final PurchaseBatchRepository _repository;

  GetOldestPurchaseBatchesUseCase(this._repository);

  Future<List<PurchaseBatch>> call(int productId) async {
    // Validation
    if (productId <= 0) {
      throw Exception('Product ID must be greater than 0');
    }

    try {
      return await _repository.getOldestActiveBatches(productId);
    } catch (e) {
      throw Exception('Failed to get oldest purchase batches: $e');
    }
  }
}
