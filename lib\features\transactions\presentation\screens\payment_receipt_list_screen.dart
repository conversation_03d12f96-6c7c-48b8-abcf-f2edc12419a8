import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared_widgets/wrappers.dart';
import '../../domain/entities/payment_receipt.dart';
import '../providers/payment_receipt_provider.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../suppliers/presentation/providers/supplier_provider.dart';

/// شاشة عرض قائمة سندات القبض والدفع
class PaymentReceiptListScreen extends StatefulWidget {
  const PaymentReceiptListScreen({super.key});

  @override
  State<PaymentReceiptListScreen> createState() =>
      _PaymentReceiptListScreenState();
}

class _PaymentReceiptListScreenState extends State<PaymentReceiptListScreen> {
  String _selectedFilter =
      'all'; // all, payment_in, payment_out, customer, supplier
  String _selectedPeriod = 'all'; // all, today, week, month

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final paymentReceiptProvider = Provider.of<PaymentReceiptProvider>(
      context,
      listen: false,
    );
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final supplierProvider = Provider.of<SupplierProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      paymentReceiptProvider.fetchPaymentReceipts(),
      customerProvider.getAllCustomers(),
      supplierProvider.getAllSuppliers(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: 'سندات القبض والدفع',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/transactions/receipts/new'),
        child: const Icon(Icons.add),
      ),
      child: Column(
        children: [
          _buildFilterSection(),
          Expanded(child: _buildReceiptsList()),
        ],
      ),
    );
  }

  /// قسم الفلترة
  Widget _buildFilterSection() {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تصفية السندات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: const InputDecoration(
                      labelText: 'نوع السند',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'all',
                        child: Text('جميع السندات'),
                      ),
                      DropdownMenuItem(
                        value: 'payment_in',
                        child: Text('سندات القبض'),
                      ),
                      DropdownMenuItem(
                        value: 'payment_out',
                        child: Text('سندات الدفع'),
                      ),
                      DropdownMenuItem(
                        value: 'customer',
                        child: Text('سندات العملاء'),
                      ),
                      DropdownMenuItem(
                        value: 'supplier',
                        child: Text('سندات الموردين'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFilter = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedPeriod,
                    decoration: const InputDecoration(
                      labelText: 'الفترة الزمنية',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'all',
                        child: Text('جميع الفترات'),
                      ),
                      DropdownMenuItem(value: 'today', child: Text('اليوم')),
                      DropdownMenuItem(
                        value: 'week',
                        child: Text('هذا الأسبوع'),
                      ),
                      DropdownMenuItem(
                        value: 'month',
                        child: Text('هذا الشهر'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قائمة السندات
  Widget _buildReceiptsList() {
    return Consumer3<
      PaymentReceiptProvider,
      CustomerProvider,
      SupplierProvider
    >(
      builder:
          (
            context,
            paymentReceiptProvider,
            customerProvider,
            supplierProvider,
            child,
          ) {
            if (paymentReceiptProvider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (paymentReceiptProvider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text(
                      paymentReceiptProvider.errorMessage!,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            final filteredReceipts = _getFilteredReceipts(
              paymentReceiptProvider,
            );

            if (filteredReceipts.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد سندات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'اضغط على زر + لإضافة سند جديد',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: _loadData,
              child: ListView.builder(
                padding: const EdgeInsets.all(16.0),
                itemCount: filteredReceipts.length,
                itemBuilder: (context, index) {
                  final receipt = filteredReceipts[index];
                  return _buildReceiptCard(
                    receipt,
                    customerProvider,
                    supplierProvider,
                  );
                },
              ),
            );
          },
    );
  }

  /// بناء بطاقة السند
  Widget _buildReceiptCard(
    PaymentReceipt receipt,
    CustomerProvider customerProvider,
    SupplierProvider supplierProvider,
  ) {
    // الحصول على اسم الكيان المرتبط
    String entityName = 'غير محدد';
    if (receipt.relatedEntityId != null) {
      if (receipt.relatedEntityType == 'customer') {
        final customer = customerProvider.customers
            .where((c) => c.id == receipt.relatedEntityId)
            .firstOrNull;
        entityName = customer?.name ?? 'عميل غير موجود';
      } else if (receipt.relatedEntityType == 'supplier') {
        final supplier = supplierProvider.suppliers
            .where((s) => s.id == receipt.relatedEntityId)
            .firstOrNull;
        entityName = supplier?.name ?? 'مورد غير موجود';
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: receipt.type == 'payment_in'
              ? Colors.green
              : Colors.red,
          child: Icon(
            receipt.type == 'payment_in'
                ? Icons.arrow_downward
                : Icons.arrow_upward,
            color: Colors.white,
          ),
        ),
        title: Text(
          receipt.typeDisplayName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${receipt.relatedEntityType == 'customer' ? 'العميل' : 'المورد'}: $entityName',
            ),
            Text(
              'التاريخ: ${DateFormat('yyyy/MM/dd').format(receipt.transactionDate)}',
            ),
            Text('طريقة الدفع: ${receipt.paymentMethod}'),
            if (receipt.description != null && receipt.description!.isNotEmpty)
              Text('الوصف: ${receipt.description}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${receipt.amount.toStringAsFixed(2)} ر.ي',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: receipt.type == 'payment_in' ? Colors.green : Colors.red,
              ),
            ),
            if (receipt.relatedInvoiceId != null)
              Text(
                'فاتورة #${receipt.relatedInvoiceId}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
          ],
        ),
        onTap: () {
          context.push('/transactions/receipts/${receipt.id}');
        },
      ),
    );
  }

  /// الحصول على السندات المفلترة
  List<PaymentReceipt> _getFilteredReceipts(PaymentReceiptProvider provider) {
    List<PaymentReceipt> receipts = provider.paymentReceipts;

    // تطبيق فلتر النوع
    switch (_selectedFilter) {
      case 'payment_in':
        receipts = provider.paymentInReceipts;
        break;
      case 'payment_out':
        receipts = provider.paymentOutReceipts;
        break;
      case 'customer':
        receipts = provider.customerReceipts;
        break;
      case 'supplier':
        receipts = provider.supplierReceipts;
        break;
      default:
        receipts = provider.paymentReceipts;
    }

    // تطبيق فلتر الفترة الزمنية
    if (_selectedPeriod != 'all') {
      receipts = provider.getReceiptsByPeriod(_selectedPeriod);

      // إعادة تطبيق فلتر النوع على النتائج المفلترة بالفترة
      if (_selectedFilter != 'all') {
        switch (_selectedFilter) {
          case 'payment_in':
            receipts = receipts.where((r) => r.type == 'payment_in').toList();
            break;
          case 'payment_out':
            receipts = receipts.where((r) => r.type == 'payment_out').toList();
            break;
          case 'customer':
            receipts = receipts
                .where((r) => r.relatedEntityType == 'customer')
                .toList();
            break;
          case 'supplier':
            receipts = receipts
                .where((r) => r.relatedEntityType == 'supplier')
                .toList();
            break;
        }
      }
    }

    // ترتيب السندات حسب التاريخ (الأحدث أولاً)
    receipts.sort((a, b) => b.transactionDate.compareTo(a.transactionDate));

    return receipts;
  }
}
