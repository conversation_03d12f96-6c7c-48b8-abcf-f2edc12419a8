import 'package:market/features/products/domain/entities/category.dart';
import 'package:market/features/products/domain/repositories/category_repository.dart';
import 'package:market/features/products/data/datasources/category_database_service.dart';
import 'package:market/features/products/data/models/category_model.dart';

class CategoryRepositoryImpl implements CategoryRepository {
  final CategoryDatabaseService _databaseService;

  CategoryRepositoryImpl(this._databaseService);

  @override
  Future<List<Category>> getCategories() async {
    try {
      final categoryModels = await _databaseService.getCategories();
      return categoryModels.map((model) => Category.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  @override
  Future<Category?> getCategoryById(int id) async {
    try {
      final categoryModel = await _databaseService.getCategoryById(id);
      if (categoryModel != null) {
        return Category.fromModel(categoryModel);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get category by id: $e');
    }
  }

  @override
  Future<void> createCategory(Category category) async {
    try {
      final categoryModel = CategoryModel(name: category.name);

      await _databaseService.createCategory(categoryModel);
    } catch (e) {
      throw Exception('Failed to create category: $e');
    }
  }

  @override
  Future<void> updateCategory(Category category) async {
    try {
      final categoryModel = CategoryModel(id: category.id, name: category.name);

      await _databaseService.updateCategory(categoryModel);
    } catch (e) {
      throw Exception('Failed to update category: $e');
    }
  }

  @override
  Future<void> deleteCategory(int id) async {
    try {
      await _databaseService.deleteCategory(id);
    } catch (e) {
      throw Exception('Failed to delete category: $e');
    }
  }

  @override
  Future<List<Category>> searchCategories(String query) async {
    try {
      final categoryModels = await _databaseService.searchCategories(query);
      return categoryModels.map((model) => Category.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to search categories: $e');
    }
  }

  @override
  Future<bool> categoryExists(String name) async {
    try {
      return await _databaseService.categoryExists(name);
    } catch (e) {
      throw Exception('Failed to check if category exists: $e');
    }
  }
}
