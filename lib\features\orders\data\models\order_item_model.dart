class OrderItemModel {
  final int? id;
  final int orderId;
  final int productId;
  final int quantity;
  final double estimatedUnitPrice;

  const OrderItemModel({
    this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.estimatedUnitPrice,
  });

  // Convert from Map (from database)
  factory OrderItemModel.fromMap(Map<String, dynamic> map) {
    return OrderItemModel(
      id: map['id'] as int?,
      orderId: map['orderId'] as int,
      productId: map['productId'] as int,
      quantity: map['quantity'] as int,
      estimatedUnitPrice: (map['estimatedUnitPrice'] as num).toDouble(),
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderId': orderId,
      'productId': productId,
      'quantity': quantity,
      'estimatedUnitPrice': estimatedUnitPrice,
    };
  }

  // Copy with method for updates
  OrderItemModel copyWith({
    int? id,
    int? orderId,
    int? productId,
    int? quantity,
    double? estimatedUnitPrice,
  }) {
    return OrderItemModel(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      estimatedUnitPrice: estimatedUnitPrice ?? this.estimatedUnitPrice,
    );
  }

  @override
  String toString() {
    return 'OrderItemModel(id: $id, orderId: $orderId, productId: $productId, '
        'quantity: $quantity, estimatedUnitPrice: $estimatedUnitPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItemModel &&
        other.id == id &&
        other.orderId == orderId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.estimatedUnitPrice == estimatedUnitPrice;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        estimatedUnitPrice.hashCode;
  }
}
