# 📁 مجلد التصدير - Market App Documentation

## 📋 محتويات المجلد

هذا المجلد يحتوي على التوثيق الشامل لتطبيق Market App بصيغ مختلفة:

### 📄 الملفات المتوفرة

1. **`Market_App_Complete_Documentation.md`**
   - التوثيق الكامل بصيغة Markdown
   - يحتوي على جميع التفاصيل التقنية والمخططات
   - مناسب للمطورين والمراجعة التقنية

2. **`Market_App_Documentation.html`**
   - نسخة HTML تفاعلية مع تصميم احترافي
   - تدعم مخططات Mermaid التفاعلية
   - مناسبة للعرض في المتصفح أو التحويل إلى PDF

3. **`README.md`** (هذا الملف)
   - دليل استخدام مجلد التصدير

## 🔄 كيفية تحويل الملفات إلى PDF

### الطريقة الأولى: استخدام المتصفح
1. افتح ملف `Market_App_Documentation.html` في أي متصفح
2. اضغط `Ctrl+P` (أو `Cmd+P` على Mac)
3. اختر "Save as PDF" من خيارات الطابعة
4. تأكد من تفعيل "Background graphics" لضمان ظهور التصميم
5. احفظ الملف باسم `Market_App_Documentation.pdf`

### الطريقة الثانية: استخدام أدوات التحويل
```bash
# باستخدام wkhtmltopdf
wkhtmltopdf --page-size A4 --orientation Portrait --margin-top 0.75in --margin-right 0.75in --margin-bottom 0.75in --margin-left 0.75in Market_App_Documentation.html Market_App_Documentation.pdf

# باستخدام Puppeteer (Node.js)
npm install puppeteer
node -e "
const puppeteer = require('puppeteer');
(async () => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  await page.goto('file://' + __dirname + '/Market_App_Documentation.html', {waitUntil: 'networkidle0'});
  await page.pdf({
    path: 'Market_App_Documentation.pdf',
    format: 'A4',
    printBackground: true,
    margin: {
      top: '20mm',
      right: '20mm',
      bottom: '20mm',
      left: '20mm'
    }
  });
  await browser.close();
})();
"
```

### الطريقة الثالثة: استخدام Pandoc
```bash
# تحويل Markdown إلى PDF
pandoc Market_App_Complete_Documentation.md -o Market_App_Documentation.pdf --pdf-engine=xelatex --variable mainfont="Arial" --variable sansfont="Arial" --variable monofont="Courier New" --variable fontsize=12pt --variable geometry:margin=2cm
```

## 📊 المخططات المتضمنة

التوثيق يحتوي على المخططات التالية:

### 1. مخطط تدفق البيانات العام
```mermaid
graph TD
    A[Presentation Layer] --> B[Domain Layer]
    B --> C[Data Layer]
    C --> D[SQLite Database]
```

### 2. مخطط تسلسل حساب رصيد العميل
```mermaid
sequenceDiagram
    participant UI as Customer Screen
    participant CP as CustomerProvider
    participant UC as GetCustomerBalanceUseCase
    participant CR as CustomerRepository
    participant DB as Database
```

### 3. مخطط العلاقات في قاعدة البيانات
```mermaid
erDiagram
    CUSTOMERS ||--o{ SALES : "has many"
    CUSTOMERS ||--o{ CUSTOMER_ACCOUNTS : "has many"
    SUPPLIERS ||--o{ PURCHASES : "has many"
```

## 🎯 استخدامات التوثيق

### للمطورين
- فهم بنية التطبيق والمعمارية المستخدمة
- مرجع لحالات الاستخدام والكيانات
- أمثلة عملية للكود
- استراتيجيات الاختبار

### لأصحاب المصلحة
- فهم أهداف التطبيق ومزاياه
- نظرة عامة على الوظائف والميزات
- إحصائيات الأداء المتوقعة
- خطة التطوير المستقبلية

### للمراجعين التقنيين
- تقييم جودة البنية المعمارية
- مراجعة المبادئ المحاسبية المطبقة
- تقييم استراتيجيات الأمان والأداء

## 📈 إحصائيات التوثيق

- **عدد الصفحات**: ~50 صفحة
- **عدد المخططات**: 3 مخططات تفاعلية
- **عدد أمثلة الكود**: 15+ مثال
- **عدد الجداول**: 5 جداول تفصيلية
- **اللغات المدعومة**: العربية (أساسي) + الإنجليزية (تقني)

## 🔧 متطلبات العرض

### لملف HTML
- متصفح حديث يدعم JavaScript
- اتصال بالإنترنت لتحميل مكتبة Mermaid
- دعم الخطوط العربية

### لملف Markdown
- محرر يدعم Markdown
- مكتبة لعرض مخططات Mermaid (اختياري)

## 📞 الدعم والمساعدة

في حالة وجود أي مشاكل في التحويل أو العرض:

1. تأكد من أن المتصفح محدث
2. تحقق من اتصال الإنترنت لتحميل المكتبات الخارجية
3. جرب متصفحات مختلفة (Chrome, Firefox, Safari)
4. تأكد من تفعيل JavaScript في المتصفح

## 📅 تاريخ التحديث

- **تاريخ الإنشاء**: ديسمبر 2024
- **آخر تحديث**: ديسمبر 2024
- **الإصدار**: 1.0

---

**ملاحظة**: هذا التوثيق يعكس الحالة الحالية للتطبيق وقد يتم تحديثه مع تطور المشروع.
