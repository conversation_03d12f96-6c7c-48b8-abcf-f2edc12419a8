import '../../domain/entities/supplier_account.dart';
import '../../domain/repositories/supplier_account_repository.dart';
import '../datasources/supplier_account_database_service.dart';
import '../models/supplier_account_model.dart';

class SupplierAccountRepositoryImpl implements SupplierAccountRepository {
  final SupplierAccountDatabaseService _databaseService;

  SupplierAccountRepositoryImpl(this._databaseService);

  @override
  Future<int> addSupplierAccountEntry(SupplierAccount entry) async {
    try {
      final entryModel = SupplierAccountModel.fromEntity(entry);
      return await _databaseService.addSupplierAccountEntry(entryModel);
    } catch (e) {
      throw Exception('Failed to add supplier account entry: $e');
    }
  }

  @override
  Future<List<SupplierAccount>> getSupplierAccountStatement(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final entryModels = await _databaseService.getSupplierAccountStatement(
        supplierId,
        fromDate: fromDate,
        toDate: toDate,
      );
      return entryModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get supplier account statement: $e');
    }
  }

  @override
  Future<List<SupplierAccount>> getAllSupplierAccountEntries() async {
    try {
      final entryModels = await _databaseService.getAllSupplierAccountEntries();
      return entryModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get all supplier account entries: $e');
    }
  }

  @override
  Future<void> deleteSupplierAccountEntry(int id) async {
    try {
      await _databaseService.deleteSupplierAccountEntry(id);
    } catch (e) {
      throw Exception('Failed to delete supplier account entry: $e');
    }
  }

  @override
  Future<void> deleteAllEntriesForSupplier(int supplierId) async {
    try {
      await _databaseService.deleteAllEntriesForSupplier(supplierId);
    } catch (e) {
      throw Exception('Failed to delete all entries for supplier: $e');
    }
  }

  @override
  Future<List<SupplierAccount>> getSupplierAccountEntries(
    int supplierId,
  ) async {
    try {
      final entryModels = await _databaseService.getSupplierAccountEntries(
        supplierId,
      );
      return entryModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get supplier account entries: $e');
    }
  }

  @override
  Future<double> getSupplierAccountBalance(int supplierId) async {
    try {
      return await _databaseService.getSupplierAccountBalance(supplierId);
    } catch (e) {
      throw Exception('Failed to get supplier account balance: $e');
    }
  }

  @override
  Future<void> deleteSupplierAccountEntries(int supplierId) async {
    return await deleteAllEntriesForSupplier(supplierId);
  }
}
