import '../entities/payment_receipt.dart';
import '../repositories/payment_receipt_repository.dart';

class CreatePaymentReceiptUseCase {
  final PaymentReceiptRepository _repository;

  CreatePaymentReceiptUseCase(this._repository);

  Future<int> call(CreatePaymentReceiptParams params) async {
    try {
      // Validate input parameters
      _validateParams(params);

      // Create PaymentReceipt entity
      final paymentReceipt = PaymentReceipt(
        relatedEntityId: params.relatedEntityId,
        relatedEntityType: params.relatedEntityType,
        transactionDate: params.transactionDate,
        amount: params.amount,
        type: params.type,
        paymentMethod: params.paymentMethod,
        description: params.description,
        relatedInvoiceId: params.relatedInvoiceId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create payment receipt in repository
      return await _repository.createPaymentReceipt(paymentReceipt);
    } catch (e) {
      throw Exception('Failed to create payment receipt: $e');
    }
  }

  void _validateParams(CreatePaymentReceiptParams params) {
    if (params.amount <= 0) {
      throw ArgumentError('Amount must be greater than zero');
    }

    if (params.relatedEntityType != 'customer' &&
        params.relatedEntityType != 'supplier') {
      throw ArgumentError(
        'Related entity type must be either "customer" or "supplier"',
      );
    }

    if (params.type != 'payment_in' && params.type != 'payment_out') {
      throw ArgumentError('Type must be either "payment_in" or "payment_out"');
    }

    // Validate consistency between entity type and payment type
    if (params.relatedEntityType == 'customer' && params.type != 'payment_in') {
      throw ArgumentError(
        'Customer transactions must be payment_in (قبض من عميل)',
      );
    }

    if (params.relatedEntityType == 'supplier' &&
        params.type != 'payment_out') {
      throw ArgumentError(
        'Supplier transactions must be payment_out (دفع لمورد)',
      );
    }

    if (params.paymentMethod.trim().isEmpty) {
      throw ArgumentError('Payment method cannot be empty');
    }
  }
}

class CreatePaymentReceiptParams {
  final int? relatedEntityId;
  final String relatedEntityType;
  final DateTime transactionDate;
  final double amount;
  final String type;
  final String paymentMethod;
  final String? description;
  final int? relatedInvoiceId;

  const CreatePaymentReceiptParams({
    this.relatedEntityId,
    required this.relatedEntityType,
    required this.transactionDate,
    required this.amount,
    required this.type,
    required this.paymentMethod,
    this.description,
    this.relatedInvoiceId,
  });

  CreatePaymentReceiptParams copyWith({
    int? relatedEntityId,
    String? relatedEntityType,
    DateTime? transactionDate,
    double? amount,
    String? type,
    String? paymentMethod,
    String? description,
    int? relatedInvoiceId,
  }) {
    return CreatePaymentReceiptParams(
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      transactionDate: transactionDate ?? this.transactionDate,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is CreatePaymentReceiptParams &&
        other.relatedEntityId == relatedEntityId &&
        other.relatedEntityType == relatedEntityType &&
        other.transactionDate == transactionDate &&
        other.amount == amount &&
        other.type == type &&
        other.paymentMethod == paymentMethod &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId;
  }

  @override
  int get hashCode {
    return relatedEntityId.hashCode ^
        relatedEntityType.hashCode ^
        transactionDate.hashCode ^
        amount.hashCode ^
        type.hashCode ^
        paymentMethod.hashCode ^
        description.hashCode ^
        relatedInvoiceId.hashCode;
  }

  @override
  String toString() {
    return 'CreatePaymentReceiptParams(relatedEntityId: $relatedEntityId, relatedEntityType: $relatedEntityType, transactionDate: $transactionDate, amount: $amount, type: $type, paymentMethod: $paymentMethod, description: $description, relatedInvoiceId: $relatedInvoiceId)';
  }
}
