#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Market App Documentation PDF Converter
======================================

هذا السكريبت يقوم بتحويل ملف HTML إلى PDF مع دعم المخططات والتصميم العربي.

المتطلبات:
- Python 3.6+
- weasyprint أو pdfkit
- wkhtmltopdf (للطريقة الثانية)

التثبيت:
pip install weasyprint
# أو
pip install pdfkit

الاستخدام:
python convert_to_pdf.py
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من توفر المتطلبات اللازمة"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print("✅ Python متوفر")
    return True

def convert_with_weasyprint():
    """تحويل HTML إلى PDF باستخدام WeasyPrint"""
    try:
        import weasyprint
        print("📄 تحويل HTML إلى PDF باستخدام WeasyPrint...")
        
        html_file = Path("Market_App_Documentation.html")
        pdf_file = Path("Market_App_Documentation_WeasyPrint.pdf")
        
        if not html_file.exists():
            print(f"❌ ملف HTML غير موجود: {html_file}")
            return False
        
        # تحويل HTML إلى PDF
        html_doc = weasyprint.HTML(filename=str(html_file))
        html_doc.write_pdf(str(pdf_file))
        
        print(f"✅ تم إنشاء PDF بنجاح: {pdf_file}")
        return True
        
    except ImportError:
        print("❌ WeasyPrint غير مثبت. قم بتثبيته باستخدام: pip install weasyprint")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحويل: {e}")
        return False

def convert_with_pdfkit():
    """تحويل HTML إلى PDF باستخدام pdfkit"""
    try:
        import pdfkit
        print("📄 تحويل HTML إلى PDF باستخدام pdfkit...")
        
        html_file = Path("Market_App_Documentation.html")
        pdf_file = Path("Market_App_Documentation_pdfkit.pdf")
        
        if not html_file.exists():
            print(f"❌ ملف HTML غير موجود: {html_file}")
            return False
        
        # إعدادات التحويل
        options = {
            'page-size': 'A4',
            'orientation': 'Portrait',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'print-media-type': None,
            'disable-smart-shrinking': None,
        }
        
        # تحويل HTML إلى PDF
        pdfkit.from_file(str(html_file), str(pdf_file), options=options)
        
        print(f"✅ تم إنشاء PDF بنجاح: {pdf_file}")
        return True
        
    except ImportError:
        print("❌ pdfkit غير مثبت. قم بتثبيته باستخدام: pip install pdfkit")
        print("💡 تحتاج أيضاً إلى تثبيت wkhtmltopdf من: https://wkhtmltopdf.org/downloads.html")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحويل: {e}")
        return False

def convert_with_browser():
    """تحويل HTML إلى PDF باستخدام متصفح Chrome/Chromium"""
    try:
        print("🌐 تحويل HTML إلى PDF باستخدام Chrome...")
        
        html_file = Path("Market_App_Documentation.html").absolute()
        pdf_file = Path("Market_App_Documentation_Chrome.pdf").absolute()
        
        if not html_file.exists():
            print(f"❌ ملف HTML غير موجود: {html_file}")
            return False
        
        # البحث عن Chrome/Chromium
        chrome_paths = [
            "google-chrome",
            "chromium-browser",
            "chromium",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
        ]
        
        chrome_path = None
        for path in chrome_paths:
            try:
                subprocess.run([path, "--version"], capture_output=True, check=True)
                chrome_path = path
                break
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        if not chrome_path:
            print("❌ لم يتم العثور على Chrome/Chromium")
            return False
        
        # أمر التحويل
        cmd = [
            chrome_path,
            "--headless",
            "--disable-gpu",
            "--print-to-pdf=" + str(pdf_file),
            "--print-to-pdf-no-header",
            "--run-all-compositor-stages-before-draw",
            "--virtual-time-budget=10000",
            "file://" + str(html_file)
        ]
        
        # تنفيذ الأمر
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and pdf_file.exists():
            print(f"✅ تم إنشاء PDF بنجاح: {pdf_file}")
            return True
        else:
            print(f"❌ فشل في التحويل: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحويل: {e}")
        return False

def create_batch_script():
    """إنشاء ملف batch للتحويل السريع"""
    batch_content = """@echo off
echo 🚀 Market App Documentation PDF Converter
echo ==========================================

echo.
echo 📄 تحويل HTML إلى PDF...

REM التحقق من وجود ملف HTML
if not exist "Market_App_Documentation.html" (
    echo ❌ ملف HTML غير موجود
    pause
    exit /b 1
)

REM محاولة التحويل باستخدام Chrome
echo 🌐 محاولة التحويل باستخدام Chrome...
"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --headless --disable-gpu --print-to-pdf="Market_App_Documentation.pdf" --print-to-pdf-no-header --run-all-compositor-stages-before-draw --virtual-time-budget=10000 "file://%CD%\\Market_App_Documentation.html"

if exist "Market_App_Documentation.pdf" (
    echo ✅ تم إنشاء PDF بنجاح!
    echo 📁 الملف: Market_App_Documentation.pdf
) else (
    echo ❌ فشل في التحويل
    echo 💡 تأكد من تثبيت Google Chrome
)

echo.
pause
"""
    
    with open("convert_to_pdf.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف convert_to_pdf.bat للتحويل السريع")

def main():
    """الدالة الرئيسية"""
    print("🚀 Market App Documentation PDF Converter")
    print("=" * 50)
    print()
    
    # التحقق من المتطلبات
    if not check_requirements():
        return
    
    # التحقق من وجود ملف HTML
    html_file = Path("Market_App_Documentation.html")
    if not html_file.exists():
        print(f"❌ ملف HTML غير موجود: {html_file}")
        print("💡 تأكد من وجود الملف في نفس المجلد")
        return
    
    print(f"📄 ملف HTML موجود: {html_file}")
    print()
    
    # محاولة طرق التحويل المختلفة
    success = False
    
    print("🔄 محاولة طرق التحويل المختلفة...")
    print()
    
    # الطريقة 1: WeasyPrint
    if convert_with_weasyprint():
        success = True
    
    # الطريقة 2: pdfkit
    if convert_with_pdfkit():
        success = True
    
    # الطريقة 3: Chrome
    if convert_with_browser():
        success = True
    
    # إنشاء ملف batch
    create_batch_script()
    
    print()
    if success:
        print("🎉 تم التحويل بنجاح!")
        print("📁 تحقق من ملفات PDF في المجلد الحالي")
    else:
        print("❌ فشل في جميع طرق التحويل")
        print("💡 جرب التحويل يدوياً من المتصفح:")
        print("   1. افتح Market_App_Documentation.html في المتصفح")
        print("   2. اضغط Ctrl+P")
        print("   3. اختر 'Save as PDF'")
    
    print()
    print("📋 ملفات التوثيق المتوفرة:")
    for file in Path(".").glob("Market_App_Documentation*"):
        print(f"   📄 {file.name}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
