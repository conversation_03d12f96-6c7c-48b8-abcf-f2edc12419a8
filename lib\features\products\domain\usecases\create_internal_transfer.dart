import '../entities/internal_transfer.dart';
import '../repositories/internal_transfer_repository.dart';

class CreateInternalTransferUseCase {
  final InternalTransferRepository _repository;

  CreateInternalTransferUseCase(this._repository);

  Future<int> call(InternalTransfer transfer) async {
    // Validation
    if (transfer.productId <= 0) {
      throw Exception('Product ID must be greater than 0');
    }

    if (transfer.quantity <= 0) {
      throw Exception('Quantity must be greater than 0');
    }

    if (transfer.retailPriceAtTransfer < 0) {
      throw Exception('Retail price cannot be negative');
    }

    if (transfer.costAtTransfer < 0) {
      throw Exception('Cost at transfer cannot be negative');
    }

    if (transfer.totalValue < 0) {
      throw Exception('Total value cannot be negative');
    }

    // Validate that totalValue matches quantity * retailPriceAtTransfer
    final expectedTotalValue =
        transfer.quantity * transfer.retailPriceAtTransfer;
    if ((transfer.totalValue - expectedTotalValue).abs() > 0.01) {
      throw Exception('Total value does not match quantity * retail price');
    }

    try {
      return await _repository.addTransfer(transfer);
    } catch (e) {
      throw Exception('Failed to create internal transfer: $e');
    }
  }
}
