import '../../../products/domain/repositories/product_repository.dart';
import '../entities/order.dart';
import '../entities/order_item.dart';

class GenerateOrderFromLowStockUseCase {
  final ProductRepository _productRepository;

  GenerateOrderFromLowStockUseCase(this._productRepository);

  Future<Map<String, dynamic>> call() async {
    try {
      // Get all products
      final products = await _productRepository.getProducts();

      // Filter products with low stock (warehouse quantity below minimum)
      final lowStockProducts = products.where((product) {
        return product.warehouseQuantity < product.minStockQuantity;
      }).toList();

      if (lowStockProducts.isEmpty) {
        throw Exception('لا توجد منتجات تحتاج إلى إعادة تخزين');
      }

      // Create order items for low stock products
      final orderItems = <OrderItem>[];
      double totalCost = 0.0;

      for (final product in lowStockProducts) {
        // Calculate suggested quantity (difference to reach minimum stock)
        final suggestedQuantity =
            product.minStockQuantity - product.warehouseQuantity;

        // Use last purchase price as estimated unit price, fallback to wholesale price
        final estimatedPrice =
            product.lastPurchasePrice ?? product.wholesalePrice;

        final orderItem = OrderItem(
          orderId: 0, // Will be set when order is created
          productId: product.id!,
          quantity: suggestedQuantity,
          estimatedUnitPrice: estimatedPrice,
        );

        orderItems.add(orderItem);
        totalCost += orderItem.totalEstimatedCost;
      }

      // Create order
      final order = Order(
        orderDate: DateTime.now(),
        totalEstimatedCost: totalCost,
        notes: 'طلبية تم إنشاؤها تلقائياً من المنتجات ذات المخزون المنخفض',
        status: 'pending',
      );

      return {
        'order': order,
        'items': orderItems,
        'lowStockProductsCount': lowStockProducts.length,
      };
    } catch (e) {
      throw Exception('Failed to generate order from low stock: $e');
    }
  }
}
