import '../entities/supplier_account.dart';
import '../repositories/supplier_account_repository.dart';

class AddSupplierAccountEntryUseCase {
  final SupplierAccountRepository _repository;

  AddSupplierAccountEntryUseCase(this._repository);

  Future<int> call(SupplierAccount entry) async {
    // Validate entry data
    if (entry.supplierId <= 0) {
      throw Exception('Supplier ID must be greater than 0');
    }

    if (entry.amount <= 0) {
      throw Exception('Amount must be greater than 0');
    }

    if (entry.type.trim().isEmpty) {
      throw Exception('Transaction type cannot be empty');
    }

    // Validate transaction type
    if (!['purchase_invoice', 'payment_out'].contains(entry.type)) {
      throw Exception(
        'Invalid transaction type. Must be purchase_invoice or payment_out',
      );
    }

    try {
      return await _repository.addSupplierAccountEntry(entry);
    } catch (e) {
      throw Exception('Failed to add supplier account entry: $e');
    }
  }
}
