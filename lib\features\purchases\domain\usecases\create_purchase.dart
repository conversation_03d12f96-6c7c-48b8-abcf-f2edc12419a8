import '../entities/purchase.dart';
import '../entities/purchase_item.dart';
import '../repositories/purchase_repository.dart';

class CreatePurchaseUseCase {
  final PurchaseRepository _repository;

  CreatePurchaseUseCase(this._repository);

  Future<int> call(Purchase purchase, List<PurchaseItem> items) async {
    // Validation
    if (items.isEmpty) {
      throw Exception('Purchase must contain at least one item');
    }

    if (purchase.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (purchase.totalPaidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (purchase.totalPaidAmount > purchase.totalAmount) {
      throw Exception('Paid amount cannot exceed total amount');
    }

    if (purchase.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (purchase.status.isEmpty) {
      throw Exception('Purchase status cannot be empty');
    }

    // Validate items
    for (final item in items) {
      if (item.productId <= 0) {
        throw Exception('Product ID must be greater than 0');
      }
      if (item.quantity <= 0) {
        throw Exception('Item quantity must be greater than 0');
      }
      if (item.unitPrice < 0) {
        throw Exception('Item unit price cannot be negative');
      }
    }

    // Validate that total matches sum of items
    final calculatedTotal = items.fold<double>(
      0.0,
      (sum, item) => sum + item.totalPrice,
    );

    if ((purchase.totalAmount - calculatedTotal).abs() > 0.01) {
      throw Exception('Total amount does not match sum of items');
    }

    // Validate payment amounts
    // dueAmount is calculated automatically in the entity: totalAmount - totalPaidAmount
    // Additional validation for business rules
    if (purchase.totalPaidAmount > purchase.totalAmount) {
      throw Exception('المبلغ المدفوع لا يمكن أن يتجاوز إجمالي الفاتورة');
    }

    try {
      return await _repository.createPurchase(purchase, items);
    } catch (e) {
      throw Exception('Failed to create purchase: $e');
    }
  }
}
