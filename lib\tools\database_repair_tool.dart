import 'package:flutter/foundation.dart';
import '../core/database/database_service.dart';

/// أداة إصلاح قاعدة البيانات المبسطة
class DatabaseRepairTool {
  static final DatabaseService _databaseService = DatabaseService.instance;

  /// تشغيل أداة الإصلاح الشاملة
  static Future<Map<String, dynamic>> runRepairTool() async {
    if (kDebugMode) {
      debugPrint('🛠️ أداة إصلاح قاعدة البيانات Market App');
      debugPrint('بدء الإصلاح المبسط...');
    }

    try {
      // إصلاح أساسي: التحقق من سلامة قاعدة البيانات
      final repairResult = await _databaseService.validateAndFixDatabase();

      if (repairResult['status'] == 'success') {
        if (kDebugMode) {
          debugPrint('✅ تم الإصلاح بنجاح');
          final fixesApplied = repairResult['fixes_applied'] as List<String>;
          for (final fix in fixesApplied) {
            debugPrint('  - $fix');
          }
        }
        return {
          'status': 'success',
          'fixes_applied': repairResult['fixes_applied'],
        };
      } else {
        if (kDebugMode) {
          debugPrint('❌ فشل في الإصلاح: ${repairResult['error']}');
        }
        return {'status': 'repair_failed', 'error': repairResult['error']};
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في أداة الإصلاح: $e');
      }
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// إعادة تعيين قاعدة البيانات بالكامل
  static Future<void> resetDatabase() async {
    if (kDebugMode) {
      debugPrint('🔄 إعادة تعيين قاعدة البيانات...');
    }

    try {
      await _databaseService.resetDatabase();

      if (kDebugMode) {
        debugPrint('✅ تم إعادة تعيين قاعدة البيانات بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ فشل في إعادة تعيين قاعدة البيانات: $e');
      }
      rethrow;
    }
  }

  /// طباعة معلومات الاستخدام
  static void printUsage() {
    if (!kDebugMode) return;

    debugPrint('🛠️ أداة إصلاح قاعدة البيانات Market App');
    debugPrint('الاستخدام:');
    debugPrint('  DatabaseRepairTool.runRepairTool()     - إصلاح شامل');
    debugPrint('  DatabaseRepairTool.resetDatabase()    - إعادة تعيين كاملة');
    debugPrint('  DatabaseRepairTool.printUsage()       - عرض هذه المساعدة');
    debugPrint('ملاحظة: هذه الأداة مبسطة بعد إزالة التقارير المتقدمة');
  }
}
