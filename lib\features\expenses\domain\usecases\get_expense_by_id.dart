import '../entities/expense.dart';
import '../repositories/expense_repository.dart';

class GetExpenseById {
  final ExpenseRepository _repository;

  GetExpenseById(this._repository);

  Future<Expense?> call(int id) async {
    if (id <= 0) {
      throw Exception('Invalid expense ID');
    }

    try {
      return await _repository.getExpenseById(id);
    } catch (e) {
      throw Exception('Failed to get expense by id: $e');
    }
  }
}
