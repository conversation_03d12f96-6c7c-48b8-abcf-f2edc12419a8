class PurchaseItemModel {
  final int? id;
  final int purchaseId;
  final int productId;
  final int quantity;
  final double unitPrice;
  final String? unit; // الوحدة (كيلو، قطعة، متر، إلخ)

  const PurchaseItemModel({
    this.id,
    required this.purchaseId,
    required this.productId,
    required this.quantity,
    required this.unitPrice,
    this.unit,
  });

  // Convert from Map (from database)
  factory PurchaseItemModel.fromMap(Map<String, dynamic> map) {
    return PurchaseItemModel(
      id: map['id'] as int?,
      purchaseId: map['purchaseId'] as int,
      productId: map['productId'] as int,
      quantity: map['quantity'] as int,
      unitPrice: (map['unitPrice'] as num).toDouble(),
      unit: map['unit'] as String?,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'purchaseId': purchaseId,
      'productId': productId,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'unit': unit,
    };
  }

  // Business logic methods
  double get totalPrice => quantity * unitPrice;

  // Copy with method for updates
  PurchaseItemModel copyWith({
    int? id,
    int? purchaseId,
    int? productId,
    int? quantity,
    double? unitPrice,
    String? unit,
  }) {
    return PurchaseItemModel(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      unit: unit ?? this.unit,
    );
  }

  @override
  String toString() {
    return 'PurchaseItemModel(id: $id, purchaseId: $purchaseId, productId: $productId, '
        'quantity: $quantity, unitPrice: $unitPrice, unit: $unit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseItemModel &&
        other.id == id &&
        other.purchaseId == purchaseId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.unit == unit;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        purchaseId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode ^
        unit.hashCode;
  }
}
