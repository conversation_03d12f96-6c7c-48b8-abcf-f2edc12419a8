import '../entities/purchase.dart';
import '../repositories/purchase_repository.dart';

class GetPurchaseByIdUseCase {
  final PurchaseRepository _repository;

  GetPurchaseByIdUseCase(this._repository);

  Future<Purchase?> call(int id) async {
    if (id <= 0) {
      throw Exception('Purchase ID must be greater than 0');
    }

    try {
      return await _repository.getPurchaseById(id);
    } catch (e) {
      throw Exception('Failed to get purchase by id: $e');
    }
  }
}
