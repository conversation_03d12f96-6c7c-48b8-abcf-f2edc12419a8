import '../entities/supplier.dart';
import '../repositories/supplier_repository.dart';

class UpdateSupplier {
  final SupplierRepository _repository;

  UpdateSupplier(this._repository);

  Future<void> call(Supplier supplier) async {
    // Validation
    if (supplier.id == null) {
      throw Exception('Supplier ID cannot be null for update');
    }

    if (supplier.name.trim().isEmpty) {
      throw Exception('Supplier name cannot be empty');
    }

    // Validate email format if provided
    if (supplier.email != null && supplier.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(supplier.email!)) {
        throw Exception('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (supplier.phone != null && supplier.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^[\d\s\-\+\(\)]+$');
      if (!phoneRegex.hasMatch(supplier.phone!)) {
        throw Exception('Invalid phone format');
      }
    }

    try {
      // Check if supplier exists
      final existingSupplier = await _repository.getSupplierById(supplier.id!);
      if (existingSupplier == null) {
        throw Exception('Supplier not found');
      }

      await _repository.updateSupplier(supplier);
    } catch (e) {
      throw Exception('Failed to update supplier: $e');
    }
  }
}
