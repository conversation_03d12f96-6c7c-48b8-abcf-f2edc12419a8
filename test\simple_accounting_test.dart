import 'package:flutter_test/flutter_test.dart';
import 'package:market/core/database/database_service.dart';

/// اختبار بسيط للتأكد من وجود الجداول المحاسبية المطلوبة
void main() {
  group('🧮 اختبار الجداول المحاسبية', () {
    late DatabaseService databaseService;

    setUp(() async {
      databaseService = DatabaseService.instance;
    });

    test('📊 التحقق من وجود جدول supplier_accounts', () async {
      final db = await databaseService.database;

      // التحقق من وجود الجدول
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='supplier_accounts'",
      );

      expect(
        result.isNotEmpty,
        true,
        reason: 'جدول supplier_accounts غير موجود',
      );
      print('✅ جدول supplier_accounts موجود');
    });

    test('💰 التحقق من وجود جدول customer_accounts', () async {
      final db = await databaseService.database;

      // التحقق من وجود الجدول
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='customer_accounts'",
      );

      expect(
        result.isNotEmpty,
        true,
        reason: 'جدول customer_accounts غير موجود',
      );
      print('✅ جدول customer_accounts موجود');
    });

    test('💸 التحقق من وجود جدول payment_receipts', () async {
      final db = await databaseService.database;

      // التحقق من وجود الجدول
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='payment_receipts'",
      );

      expect(
        result.isNotEmpty,
        true,
        reason: 'جدول payment_receipts غير موجود',
      );
      print('✅ جدول payment_receipts موجود');
    });

    test('🛒 التحقق من وجود جدول purchases', () async {
      final db = await databaseService.database;

      // التحقق من وجود الجدول
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='purchases'",
      );

      expect(result.isNotEmpty, true, reason: 'جدول purchases غير موجود');
      print('✅ جدول purchases موجود');
    });

    test('📋 التحقق من وجود جدول purchase_items', () async {
      final db = await databaseService.database;

      // التحقق من وجود الجدول
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_items'",
      );

      expect(result.isNotEmpty, true, reason: 'جدول purchase_items غير موجود');
      print('✅ جدول purchase_items موجود');
    });

    test('🏢 التحقق من هيكل جدول supplier_accounts', () async {
      final db = await databaseService.database;

      // التحقق من أعمدة الجدول
      final result = await db.rawQuery("PRAGMA table_info(supplier_accounts)");

      final columnNames = result.map((row) => row['name'] as String).toList();

      expect(columnNames.contains('id'), true);
      expect(columnNames.contains('supplierId'), true);
      expect(columnNames.contains('transactionDate'), true);
      expect(columnNames.contains('type'), true);
      expect(columnNames.contains('amount'), true);
      expect(columnNames.contains('description'), true);
      expect(columnNames.contains('relatedInvoiceId'), true);

      print('✅ هيكل جدول supplier_accounts صحيح');
      print('الأعمدة: ${columnNames.join(', ')}');
    });

    test('👥 التحقق من هيكل جدول customer_accounts', () async {
      final db = await databaseService.database;

      // التحقق من أعمدة الجدول
      final result = await db.rawQuery("PRAGMA table_info(customer_accounts)");

      final columnNames = result.map((row) => row['name'] as String).toList();

      expect(columnNames.contains('id'), true);
      expect(columnNames.contains('customerId'), true);
      expect(columnNames.contains('transactionDate'), true);
      expect(columnNames.contains('type'), true);
      expect(columnNames.contains('amount'), true);
      expect(columnNames.contains('description'), true);
      expect(columnNames.contains('relatedInvoiceId'), true);
      expect(columnNames.contains('isPaid'), true);

      print('✅ هيكل جدول customer_accounts صحيح');
      print('الأعمدة: ${columnNames.join(', ')}');
    });

    test('🧾 التحقق من هيكل جدول payment_receipts', () async {
      final db = await databaseService.database;

      // التحقق من أعمدة الجدول
      final result = await db.rawQuery("PRAGMA table_info(payment_receipts)");

      final columnNames = result.map((row) => row['name'] as String).toList();

      expect(columnNames.contains('id'), true);
      expect(columnNames.contains('relatedEntityId'), true);
      expect(columnNames.contains('relatedEntityType'), true);
      expect(columnNames.contains('transactionDate'), true);
      expect(columnNames.contains('amount'), true);
      expect(columnNames.contains('type'), true);
      expect(columnNames.contains('paymentMethod'), true);
      expect(columnNames.contains('description'), true);
      expect(columnNames.contains('relatedInvoiceId'), true);
      expect(columnNames.contains('createdAt'), true);
      expect(columnNames.contains('updatedAt'), true);

      print('✅ هيكل جدول payment_receipts صحيح');
      print('الأعمدة: ${columnNames.join(', ')}');
    });

    test('📊 اختبار إدراج بيانات تجريبية في supplier_accounts', () async {
      final db = await databaseService.database;

      // إدراج بيانات تجريبية
      final testData = {
        'supplierId': 1,
        'transactionDate': DateTime.now().toIso8601String(),
        'type': 'purchase_invoice',
        'amount': 1000.0,
        'description': 'اختبار فاتورة شراء',
        'relatedInvoiceId': 123,
      };

      final id = await db.insert('supplier_accounts', testData);
      expect(id, greaterThan(0));

      // التحقق من الإدراج
      final result = await db.query(
        'supplier_accounts',
        where: 'id = ?',
        whereArgs: [id],
      );
      expect(result.length, 1);
      expect(result.first['amount'], 1000.0);
      expect(result.first['type'], 'purchase_invoice');

      // حذف البيانات التجريبية
      await db.delete('supplier_accounts', where: 'id = ?', whereArgs: [id]);

      print('✅ اختبار إدراج البيانات في supplier_accounts نجح');
    });

    test('💰 اختبار إدراج بيانات تجريبية في customer_accounts', () async {
      final db = await databaseService.database;

      // إدراج بيانات تجريبية
      final testData = {
        'customerId': 1,
        'transactionDate': DateTime.now().toIso8601String(),
        'type': 'payment_in',
        'amount': 500.0,
        'description': 'اختبار سند قبض',
        'relatedInvoiceId': 456,
        'isPaid': 1,
      };

      final id = await db.insert('customer_accounts', testData);
      expect(id, greaterThan(0));

      // التحقق من الإدراج
      final result = await db.query(
        'customer_accounts',
        where: 'id = ?',
        whereArgs: [id],
      );
      expect(result.length, 1);
      expect(result.first['amount'], 500.0);
      expect(result.first['type'], 'payment_in');

      // حذف البيانات التجريبية
      await db.delete('customer_accounts', where: 'id = ?', whereArgs: [id]);

      print('✅ اختبار إدراج البيانات في customer_accounts نجح');
    });

    test('🧾 اختبار إدراج بيانات تجريبية في payment_receipts', () async {
      final db = await databaseService.database;

      // إدراج بيانات تجريبية
      final testData = {
        'relatedEntityId': 1,
        'relatedEntityType': 'customer',
        'transactionDate': DateTime.now().toIso8601String(),
        'amount': 750.0,
        'type': 'payment_in',
        'paymentMethod': 'نقدي',
        'description': 'اختبار سند قبض',
        'relatedInvoiceId': 789,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      final id = await db.insert('payment_receipts', testData);
      expect(id, greaterThan(0));

      // التحقق من الإدراج
      final result = await db.query(
        'payment_receipts',
        where: 'id = ?',
        whereArgs: [id],
      );
      expect(result.length, 1);
      expect(result.first['amount'], 750.0);
      expect(result.first['type'], 'payment_in');
      expect(result.first['relatedEntityType'], 'customer');

      // حذف البيانات التجريبية
      await db.delete('payment_receipts', where: 'id = ?', whereArgs: [id]);

      print('✅ اختبار إدراج البيانات في payment_receipts نجح');
    });
  });
}
