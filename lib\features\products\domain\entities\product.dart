import 'package:market/features/products/data/models/product_model.dart';

class Product {
  final int? id;
  final String name;
  final String? description;
  final String category;
  final String unit;
  final double?
  lastPurchasePrice; // آخر سعر شراء للوحدة، يمكن أن يكون null في البداية
  final double wholesalePrice; // سعر البيع بالجملة
  final double retailPrice; // سعر البيع بالتجزئة
  final int minStockQuantity;
  final String? barcode;
  final int warehouseQuantity;
  final int storeQuantity;

  const Product({
    this.id,
    required this.name,
    this.description,
    required this.category,
    required this.unit,
    this.lastPurchasePrice,
    required this.wholesalePrice,
    required this.retailPrice,
    required this.minStockQuantity,
    this.barcode,
    required this.warehouseQuantity,
    required this.storeQuantity,
  });

  // Create Product from ProductModel
  factory Product.fromModel(ProductModel model) {
    return Product(
      id: model.id,
      name: model.name,
      description: model.description,
      category: model.category,
      unit: model.unit,
      lastPurchasePrice: model.lastPurchasePrice,
      wholesalePrice: model.wholesalePrice,
      retailPrice: model.retailPrice,
      minStockQuantity: model.minStockQuantity,
      barcode: model.barcode,
      warehouseQuantity: model.warehouseQuantity,
      storeQuantity: model.storeQuantity,
    );
  }

  // Business logic methods
  int get totalQuantity => warehouseQuantity + storeQuantity;

  bool get isLowStock => totalQuantity <= minStockQuantity;

  bool get isOutOfStock => totalQuantity <= 0;

  double get profitMargin =>
      lastPurchasePrice != null ? wholesalePrice - lastPurchasePrice! : 0;

  double get profitPercentage =>
      lastPurchasePrice != null && lastPurchasePrice! > 0
      ? (profitMargin / lastPurchasePrice!) * 100
      : 0;

  // Copy with method for updates
  Product copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    String? unit,
    double? lastPurchasePrice,
    double? wholesalePrice,
    double? retailPrice,
    int? minStockQuantity,
    String? barcode,
    int? warehouseQuantity,
    int? storeQuantity,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      lastPurchasePrice: lastPurchasePrice ?? this.lastPurchasePrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      retailPrice: retailPrice ?? this.retailPrice,
      minStockQuantity: minStockQuantity ?? this.minStockQuantity,
      barcode: barcode ?? this.barcode,
      warehouseQuantity: warehouseQuantity ?? this.warehouseQuantity,
      storeQuantity: storeQuantity ?? this.storeQuantity,
    );
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, category: $category, '
        'totalQuantity: $totalQuantity, wholesalePrice: $wholesalePrice, retailPrice: $retailPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
