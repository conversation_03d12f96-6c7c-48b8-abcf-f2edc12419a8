import '../entities/inventory_count.dart';
import '../repositories/inventory_count_repository.dart';

class GetInventoryCountByIdUseCase {
  final InventoryCountRepository _repository;

  GetInventoryCountByIdUseCase(this._repository);

  Future<InventoryCount?> call(int id) async {
    if (id <= 0) {
      throw Exception('Inventory count ID must be greater than 0');
    }

    try {
      return await _repository.getInventoryCountById(id);
    } catch (e) {
      throw Exception('Failed to get inventory count by id: $e');
    }
  }
}
