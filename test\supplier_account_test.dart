import 'package:flutter_test/flutter_test.dart';
import 'package:market/features/suppliers/domain/entities/supplier_account.dart';
import 'package:market/features/suppliers/data/models/supplier_account_model.dart';

void main() {
  group('SupplierAccount Tests', () {
    test('should create SupplierAccount entity correctly', () {
      // Arrange
      final now = DateTime.now();
      final supplierAccount = SupplierAccount(
        id: 1,
        supplierId: 1,
        transactionDate: now,
        type: 'purchase_invoice',
        amount: 1000.0,
        description: 'فاتورة شراء رقم #1',
        relatedInvoiceId: 1,
      );

      // Assert
      expect(supplierAccount.id, 1);
      expect(supplierAccount.supplierId, 1);
      expect(supplierAccount.transactionDate, now);
      expect(supplierAccount.type, 'purchase_invoice');
      expect(supplierAccount.amount, 1000.0);
      expect(supplierAccount.description, 'فاتورة شراء رقم #1');
      expect(supplierAccount.relatedInvoiceId, 1);
      expect(supplierAccount.isDebit, true);
      expect(supplierAccount.isCredit, false);
    });

    test('should identify payment_out as credit transaction', () {
      // Arrange
      final supplierAccount = SupplierAccount(
        supplierId: 1,
        transactionDate: DateTime.now(),
        type: 'payment_out',
        amount: 500.0,
        description: 'سند دفع للمورد',
      );

      // Assert
      expect(supplierAccount.isDebit, false);
      expect(supplierAccount.isCredit, true);
    });

    test(
      'should convert SupplierAccount to SupplierAccountModel correctly',
      () {
        // Arrange
        final now = DateTime.now();
        final supplierAccount = SupplierAccount(
          id: 1,
          supplierId: 1,
          transactionDate: now,
          type: 'purchase_invoice',
          amount: 1000.0,
          description: 'فاتورة شراء رقم #1',
          relatedInvoiceId: 1,
        );

        // Act
        final model = SupplierAccountModel.fromEntity(supplierAccount);

        // Assert
        expect(model.id, supplierAccount.id);
        expect(model.supplierId, supplierAccount.supplierId);
        expect(model.transactionDate, supplierAccount.transactionDate);
        expect(model.type, supplierAccount.type);
        expect(model.amount, supplierAccount.amount);
        expect(model.description, supplierAccount.description);
        expect(model.relatedInvoiceId, supplierAccount.relatedInvoiceId);
      },
    );

    test(
      'should convert SupplierAccountModel to SupplierAccount correctly',
      () {
        // Arrange
        final now = DateTime.now();
        final model = SupplierAccountModel(
          id: 1,
          supplierId: 1,
          transactionDate: now,
          type: 'payment_out',
          amount: 500.0,
          description: 'سند دفع للمورد',
          relatedInvoiceId: null,
        );

        // Act
        final entity = model.toEntity();

        // Assert
        expect(entity.id, model.id);
        expect(entity.supplierId, model.supplierId);
        expect(entity.transactionDate, model.transactionDate);
        expect(entity.type, model.type);
        expect(entity.amount, model.amount);
        expect(entity.description, model.description);
        expect(entity.relatedInvoiceId, model.relatedInvoiceId);
      },
    );

    test('should convert SupplierAccountModel to Map correctly', () {
      // Arrange
      final now = DateTime.now();
      final model = SupplierAccountModel(
        id: 1,
        supplierId: 1,
        transactionDate: now,
        type: 'purchase_invoice',
        amount: 1000.0,
        description: 'فاتورة شراء رقم #1',
        relatedInvoiceId: 1,
      );

      // Act
      final map = model.toMap();

      // Assert
      expect(map['id'], 1);
      expect(map['supplierId'], 1);
      expect(map['transactionDate'], now.toIso8601String());
      expect(map['type'], 'purchase_invoice');
      expect(map['amount'], 1000.0);
      expect(map['description'], 'فاتورة شراء رقم #1');
      expect(map['relatedInvoiceId'], 1);
    });

    test('should create SupplierAccountModel from Map correctly', () {
      // Arrange
      final now = DateTime.now();
      final map = {
        'id': 1,
        'supplierId': 1,
        'transactionDate': now.toIso8601String(),
        'type': 'payment_out',
        'amount': 500.0,
        'description': 'سند دفع للمورد',
        'relatedInvoiceId': null,
      };

      // Act
      final model = SupplierAccountModel.fromMap(map);

      // Assert
      expect(model.id, 1);
      expect(model.supplierId, 1);
      expect(model.transactionDate, now);
      expect(model.type, 'payment_out');
      expect(model.amount, 500.0);
      expect(model.description, 'سند دفع للمورد');
      expect(model.relatedInvoiceId, null);
    });
  });
}
