import '../entities/inventory_count.dart';
import '../repositories/inventory_count_repository.dart';

class CreateInventoryCountUseCase {
  final InventoryCountRepository _repository;

  CreateInventoryCountUseCase(this._repository);

  Future<int> call(InventoryCount inventoryCount) async {
    // Validation
    if (inventoryCount.productId <= 0) {
      throw Exception('Product ID must be greater than 0');
    }

    if (inventoryCount.countedWarehouseQuantity < 0) {
      throw Exception('Counted warehouse quantity cannot be negative');
    }

    if (inventoryCount.countedStoreQuantity < 0) {
      throw Exception('Counted store quantity cannot be negative');
    }

    if (inventoryCount.systemWarehouseQuantity < 0) {
      throw Exception('System warehouse quantity cannot be negative');
    }

    if (inventoryCount.systemStoreQuantity < 0) {
      throw Exception('System store quantity cannot be negative');
    }

    // Validate that differences are calculated correctly
    final expectedWarehouseDifference =
        inventoryCount.countedWarehouseQuantity -
        inventoryCount.systemWarehouseQuantity;
    final expectedStoreDifference =
        inventoryCount.countedStoreQuantity -
        inventoryCount.systemStoreQuantity;

    if (inventoryCount.warehouseDifference != expectedWarehouseDifference) {
      throw Exception('Warehouse difference calculation is incorrect');
    }

    if (inventoryCount.storeDifference != expectedStoreDifference) {
      throw Exception('Store difference calculation is incorrect');
    }

    try {
      return await _repository.createInventoryCount(inventoryCount);
    } catch (e) {
      throw Exception('Failed to create inventory count: $e');
    }
  }
}
