import '../entities/order.dart';
import '../repositories/order_repository.dart';

class GetOrderByIdUseCase {
  final OrderRepository _repository;

  GetOrderByIdUseCase(this._repository);

  Future<Order?> call(int id) async {
    if (id <= 0) {
      throw Exception('Order ID must be greater than 0');
    }

    try {
      return await _repository.getOrderById(id);
    } catch (e) {
      throw Exception('Failed to get order by id: $e');
    }
  }
}
