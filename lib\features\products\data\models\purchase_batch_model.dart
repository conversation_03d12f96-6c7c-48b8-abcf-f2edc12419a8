class PurchaseBatchModel {
  final int? id;
  final int productId;
  final DateTime purchaseDate;
  final int quantity;
  final double unitPurchasePrice;
  final int remainingQuantity;
  final bool isActive;

  const PurchaseBatchModel({
    this.id,
    required this.productId,
    required this.purchaseDate,
    required this.quantity,
    required this.unitPurchasePrice,
    required this.remainingQuantity,
    required this.isActive,
  });

  // Convert from Map (from database)
  factory PurchaseBatchModel.fromMap(Map<String, dynamic> map) {
    return PurchaseBatchModel(
      id: map['id'] as int?,
      productId: map['productId'] as int,
      purchaseDate: DateTime.parse(map['purchaseDate'] as String),
      quantity: map['quantity'] as int,
      unitPurchasePrice: (map['unitPurchasePrice'] as num).toDouble(),
      remainingQuantity: map['remainingQuantity'] as int,
      isActive: (map['isActive'] as int) == 1,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'purchaseDate': purchaseDate.toIso8601String(),
      'quantity': quantity,
      'unitPurchasePrice': unitPurchasePrice,
      'remainingQuantity': remainingQuantity,
      'isActive': isActive ? 1 : 0,
    };
  }

  // Copy with method for updates
  PurchaseBatchModel copyWith({
    int? id,
    int? productId,
    DateTime? purchaseDate,
    int? quantity,
    double? unitPurchasePrice,
    int? remainingQuantity,
    bool? isActive,
  }) {
    return PurchaseBatchModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      quantity: quantity ?? this.quantity,
      unitPurchasePrice: unitPurchasePrice ?? this.unitPurchasePrice,
      remainingQuantity: remainingQuantity ?? this.remainingQuantity,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'PurchaseBatchModel(id: $id, productId: $productId, '
        'purchaseDate: $purchaseDate, quantity: $quantity, '
        'unitPurchasePrice: $unitPurchasePrice, remainingQuantity: $remainingQuantity, '
        'isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseBatchModel &&
        other.id == id &&
        other.productId == productId &&
        other.purchaseDate == purchaseDate &&
        other.quantity == quantity &&
        other.unitPurchasePrice == unitPurchasePrice &&
        other.remainingQuantity == remainingQuantity &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        purchaseDate.hashCode ^
        quantity.hashCode ^
        unitPurchasePrice.hashCode ^
        remainingQuantity.hashCode ^
        isActive.hashCode;
  }
}
