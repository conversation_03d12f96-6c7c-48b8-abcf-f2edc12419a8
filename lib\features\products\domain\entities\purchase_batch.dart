import '../../data/models/purchase_batch_model.dart';

class PurchaseBatch {
  final int? id;
  final int productId;
  final DateTime purchaseDate;
  final int quantity;
  final double unitPurchasePrice;
  final int remainingQuantity;
  final bool isActive;

  const PurchaseBatch({
    this.id,
    required this.productId,
    required this.purchaseDate,
    required this.quantity,
    required this.unitPurchasePrice,
    required this.remainingQuantity,
    required this.isActive,
  });

  // Create PurchaseBatch from PurchaseBatchModel
  factory PurchaseBatch.fromModel(PurchaseBatchModel model) {
    return PurchaseBatch(
      id: model.id,
      productId: model.productId,
      purchaseDate: model.purchaseDate,
      quantity: model.quantity,
      unitPurchasePrice: model.unitPurchasePrice,
      remainingQuantity: model.remainingQuantity,
      isActive: model.isActive,
    );
  }

  // Business logic methods
  double get totalValue => quantity * unitPurchasePrice;
  double get remainingValue => remainingQuantity * unitPurchasePrice;
  bool get isFullyConsumed => remainingQuantity <= 0;
  bool get hasRemainingQuantity => remainingQuantity > 0 && isActive;

  // Copy with method for updates
  PurchaseBatch copyWith({
    int? id,
    int? productId,
    DateTime? purchaseDate,
    int? quantity,
    double? unitPurchasePrice,
    int? remainingQuantity,
    bool? isActive,
  }) {
    return PurchaseBatch(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      quantity: quantity ?? this.quantity,
      unitPurchasePrice: unitPurchasePrice ?? this.unitPurchasePrice,
      remainingQuantity: remainingQuantity ?? this.remainingQuantity,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'PurchaseBatch(id: $id, productId: $productId, '
        'purchaseDate: $purchaseDate, quantity: $quantity, '
        'unitPurchasePrice: $unitPurchasePrice, remainingQuantity: $remainingQuantity, '
        'isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseBatch &&
        other.id == id &&
        other.productId == productId &&
        other.purchaseDate == purchaseDate &&
        other.quantity == quantity &&
        other.unitPurchasePrice == unitPurchasePrice &&
        other.remainingQuantity == remainingQuantity &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        purchaseDate.hashCode ^
        quantity.hashCode ^
        unitPurchasePrice.hashCode ^
        remainingQuantity.hashCode ^
        isActive.hashCode;
  }
}
