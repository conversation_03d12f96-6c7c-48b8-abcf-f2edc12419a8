import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import '../../features/sales/domain/entities/sale.dart';
import '../../features/sales/domain/entities/sale_item.dart';
import '../../features/purchases/domain/entities/purchase.dart';
import '../../features/purchases/domain/entities/purchase_item.dart';
import '../../features/customers/domain/entities/customer.dart';
import '../../features/suppliers/domain/entities/supplier.dart';

/// خدمة تصدير PDF للفواتير والتقارير
class PDFService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0.00');

  static late pw.Font _notoRegularFont;
  static late pw.Font _notoBoldFont;
  static bool _fontsLoaded = false;

  static Future<void> loadFonts() async {
    if (_fontsLoaded) return;
    try {
      final ByteData notoRegular = await rootBundle.load(
        'assets/fonts/noto_arabic/NotoSansArabic-Regular.ttf',
      );
      _notoRegularFont = pw.Font.ttf(notoRegular);
      final ByteData notoBold = await rootBundle.load(
        'assets/fonts/noto_arabic/NotoSansArabic-Bold.ttf',
      );
      _notoBoldFont = pw.Font.ttf(notoBold);

      _fontsLoaded = true;
      debugPrint('✅ تم تحميل خطوط Noto بنجاح.');
    } catch (e) {
      debugPrint('❌ فشل تحميل الخطوط: $e. سيتم استخدام الخطوط الافتراضية.');
      _fontsLoaded = false;
      _notoRegularFont = pw.Font.helvetica();
      _notoBoldFont = pw.Font.helveticaBold();
    }
  }

  /// دالة مساعدة لاختيار الخط المناسب
  static pw.Font _getAppropriateFont(String text, {bool isBold = false}) {
    if (!_fontsLoaded) {
      return isBold ? pw.Font.helveticaBold() : pw.Font.helvetica();
    }
    return isBold ? _notoBoldFont : _notoRegularFont;
  }

  /// تصدير فاتورة مبيعات إلى PDF
  static Future<Uint8List> generateSaleInvoicePDF({
    required Sale sale,
    required List<SaleItem> items,
    Customer? customer,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              _buildInvoiceHeaderV2(
                invoiceTitle: 'فاتورة مبيعات',
                invoiceNumber: sale.id?.toString() ?? '---',
                invoiceDate: sale.saleDate,
                customer: customer,
                supplier: null,
                paymentType: sale.paymentMethodDisplayName,
              ),
              pw.SizedBox(height: 20),
              _buildSaleItemsTableV2(items),
              pw.SizedBox(height: 20),
              _buildSaleTotalsV2(sale),
              pw.Spacer(),
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// تصدير فاتورة مشتريات إلى PDF
  static Future<Uint8List> generatePurchaseInvoicePDF({
    required Purchase purchase,
    required List<PurchaseItem> items,
    Supplier? supplier,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              _buildInvoiceHeaderV2(
                invoiceTitle: 'فاتورة مشتريات',
                invoiceNumber: purchase.id?.toString() ?? '---',
                invoiceDate: purchase.purchaseDate,
                customer: null,
                supplier: supplier,
                paymentType: purchase.paymentMethodDisplayName,
              ),
              pw.SizedBox(height: 20),
              _buildPurchaseItemsTableV2(items),
              pw.SizedBox(height: 20),
              _buildPurchaseTotalsV2(purchase),
              pw.Spacer(),
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// توليد PDF لكشف حساب المورد
  static Future<Uint8List> generateSupplierStatementPDF({
    required Supplier supplier,
    required List<dynamic> statementItems,
    DateTime? fromDate,
    DateTime? toDate,
    double openingBalance = 0.0,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.stretch,
            children: [
              _buildSupplierStatementHeader(
                supplier: supplier,
                fromDate: fromDate,
                toDate: toDate,
              ),
              pw.SizedBox(height: 20),
              _buildStatementTable(statementItems, openingBalance),
              pw.Spacer(),
              _buildStatementFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// طباعة PDF
  static Future<void> printPDF(Uint8List pdfData, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: title,
    );
  }

  /// حفظ PDF في الجهاز
  static Future<String> savePDF(Uint8List pdfData, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName.pdf');
    await file.writeAsBytes(pdfData);
    return file.path;
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfData, String fileName) async {
    await Printing.sharePdf(bytes: pdfData, filename: '$fileName.pdf');
  }

  /// توليد PDF لكشف حساب العميل
  static Future<Uint8List> generateCustomerStatementPDF({
    required Customer customer,
    required List<dynamic> statementItems,
    DateTime? fromDate,
    DateTime? toDate,
    double openingBalance = 0.0,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.stretch,
            children: [
              _buildStatementHeader(
                customer: customer,
                fromDate: fromDate,
                toDate: toDate,
              ),
              pw.SizedBox(height: 20),
              _buildStatementTable(statementItems, openingBalance),
              pw.Spacer(),
              _buildStatementFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء الترويسة المشتركة العلوية
  static pw.Widget _buildCommonHeader() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'أسامة ماركت',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('أسامة ماركت'),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'للمواد الغذائية',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('للمواد الغذائية'),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'محطة المسعودي - الشارع العام',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('محطة المسعودي - الشارع العام'),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  '739740717 - 779031849',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('739740717 - 779031849'),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Osama Market',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('Osama Market'),
                  ),
                  textDirection: pw.TextDirection.ltr,
                  textAlign: pw.TextAlign.left,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'For Food Supplies',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('For Food Supplies'),
                  ),
                  textDirection: pw.TextDirection.ltr,
                  textAlign: pw.TextAlign.left,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'Al-Masoudi Station - Main Street',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont(
                      'Al-Masoudi Station - Main Street',
                    ),
                  ),
                  textDirection: pw.TextDirection.ltr,
                  textAlign: pw.TextAlign.left,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  '739740717 - 779031849',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: _getAppropriateFont('739740717 - 779031849'),
                  ),
                  textDirection: pw.TextDirection.ltr,
                  textAlign: pw.TextAlign.left,
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 15),
        pw.Divider(height: 1, color: PdfColors.grey),
        pw.SizedBox(height: 20),
      ],
    );
  }

  /// بناء رأس كشف الحساب
  static pw.Widget _buildStatementHeader({
    required Customer customer,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    final String dateRange = fromDate != null && toDate != null
        ? 'من ${_dateFormat.format(fromDate)} إلى ${_dateFormat.format(toDate)}'
        : 'جميع الفترات';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch,
      children: [
        _buildCommonHeader(),
        pw.Center(
          child: pw.Text(
            'كشف حساب العميل',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              font: _getAppropriateFont('كشف حساب العميل', isBold: true),
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
        pw.SizedBox(height: 15),

        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'الأخ: ${customer.name}',
              style: pw.TextStyle(
                fontSize: 12,
                font: _getAppropriateFont('الأخ: ${customer.name}'),
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.right,
            ),
            pw.Text(
              'الفترة: $dateRange',
              style: pw.TextStyle(
                fontSize: 12,
                font: _getAppropriateFont('الفترة: $dateRange'),
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.left,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء رأس كشف حساب المورد
  static pw.Widget _buildSupplierStatementHeader({
    required Supplier supplier,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    final String dateRange = fromDate != null && toDate != null
        ? 'من ${_dateFormat.format(fromDate)} إلى ${_dateFormat.format(toDate)}'
        : 'جميع الفترات';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch,
      children: [
        _buildCommonHeader(),
        pw.Center(
          child: pw.Text(
            'كشف حساب المورد',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              font: _getAppropriateFont('كشف حساب المورد', isBold: true),
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
        pw.SizedBox(height: 15),

        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'المورد: ${supplier.name}',
              style: pw.TextStyle(
                fontSize: 12,
                font: _getAppropriateFont('المورد: ${supplier.name}'),
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.right,
            ),
            pw.Text(
              'الفترة: $dateRange',
              style: pw.TextStyle(
                fontSize: 12,
                font: _getAppropriateFont('الفترة: $dateRange'),
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.left,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جدول كشف الحساب
  static pw.Widget _buildStatementTable(
    List<dynamic> items,
    double openingBalance,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      columnWidths: const {
        0: pw.FlexColumnWidth(2), // التاريخ
        1: pw.FlexColumnWidth(3), // البيان
        2: pw.FlexColumnWidth(2), // مدين
        3: pw.FlexColumnWidth(2), // دائن
        4: pw.FlexColumnWidth(2), // الرصيد
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCellV2(
              'التاريخ',
              isHeader: true,
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
            _buildTableCellV2(
              'البيان',
              isHeader: true,
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
            _buildTableCellV2(
              'مدين',
              isHeader: true,
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'دائن',
              isHeader: true,
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'الرصيد',
              isHeader: true,
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.ltr,
            ),
          ],
        ),

        // صف الرصيد الافتتاحي
        if (openingBalance != 0.0)
          pw.TableRow(
            children: [
              _buildTableCellV2(
                '',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                'الرصيد الافتتاحي',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                openingBalance > 0
                    ? _currencyFormat.format(openingBalance)
                    : '',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                openingBalance < 0
                    ? _currencyFormat.format(-openingBalance)
                    : '',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                _currencyFormat.format(openingBalance),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
            ],
          ),

        // صفوف البيانات
        ...items.map((item) {
          // نحتاج للوصول لخصائص العنصر بشكل ديناميكي
          final date = _dateFormat.format(item.transaction.transactionDate);
          final description = item.transaction.description ?? 'بدون وصف';
          final debit = item.debit > 0
              ? _currencyFormat.format(item.debit)
              : '';
          final credit = item.credit > 0
              ? _currencyFormat.format(item.credit)
              : '';
          final balance = _currencyFormat.format(item.runningBalance);

          return pw.TableRow(
            children: [
              _buildTableCellV2(
                date,
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                description,
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                debit,
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                credit,
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                balance,
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء تذييل كشف الحساب
  static pw.Widget _buildStatementFooter() {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Text(
        'تم إنشاء هذا الكشف بواسطة نظام أسامة ماركت',
        style: pw.TextStyle(
          fontSize: 10,
          font: _getAppropriateFont(
            'تم إنشاء هذا الكشف بواسطة نظام أسامة ماركت',
          ),
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.right,
      ),
    );
  }

  /// بناء رأس الفاتورة الجديد ثنائي اللغة
  static pw.Widget _buildInvoiceHeaderV2({
    required String invoiceTitle,
    required String invoiceNumber,
    required DateTime invoiceDate,
    Customer? customer,
    Supplier? supplier,
    required String paymentType,
  }) {
    final String entityLabelPrefix = customer != null
        ? 'الأخ: '
        : (supplier != null ? 'المورد: ' : '');
    final String entityName =
        customer?.name ?? supplier?.name ?? 'عميل/مورد عابر';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch,
      children: [
        _buildCommonHeader(),
        pw.Center(
          child: pw.Column(
            children: [
              pw.Text(
                invoiceTitle,
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  font: _getAppropriateFont(invoiceTitle, isBold: true),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                paymentType,
                style: pw.TextStyle(
                  fontSize: 14,
                  font: _getAppropriateFont(paymentType),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 15),

        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              '$entityLabelPrefix$entityName',
              style: pw.TextStyle(
                fontSize: 12,
                font: _getAppropriateFont('$entityLabelPrefix$entityName'),
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.right,
            ),

            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'التاريخ: ${_dateFormat.format(invoiceDate)}',
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _getAppropriateFont(
                      'التاريخ: ${_dateFormat.format(invoiceDate)}',
                    ),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  'رقم الفاتورة: $invoiceNumber',
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _getAppropriateFont('رقم الفاتورة: $invoiceNumber'),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جدول العناصر الجديد للمبيعات - يطابق النموذج المرفق
  static pw.Widget _buildSaleItemsTableV2(List<SaleItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.2), // القيمة
        1: const pw.FlexColumnWidth(1.2), // السعر
        2: const pw.FlexColumnWidth(1), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.5), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2(
              'القيمة',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'السعر',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'الكمية',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'البيان',
              isHeader: true,
              textDirection: pw.TextDirection.rtl,
            ),
            _buildTableCellV2(
              'م',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null && item.unit!.isNotEmpty
              ? ' ${item.unit}'
              : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(
                _currencyFormat.format(item.totalPrice),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                _currencyFormat.format(item.unitPrice),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                '${item.quantity ?? 1}$unitText',
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                item.description ?? 'منتج رقم ${item.productId ?? ''}',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                index.toString(),
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.ltr,
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء خلية الجدول الجديدة
  static pw.Widget _buildTableCellV2(
    String text, {
    bool isHeader = false,
    pw.TextAlign? textAlign,
    pw.TextDirection? textDirection,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          font: _getAppropriateFont(text, isBold: isHeader),
        ),
        textAlign: textAlign ?? pw.TextAlign.center,
        textDirection: textDirection,
      ),
    );
  }

  /// بناء إجماليات المبيعات الجديدة - يطابق النموذج المرفق
  static pw.Widget _buildSaleTotalsV2(Sale sale) {
    return pw.Column(
      children: [
        pw.SizedBox(height: 16),

        // الإجمالي محاذاة لليمين
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.symmetric(vertical: 12),
          child: pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Row(
              mainAxisSize: pw.MainAxisSize.min,
              children: [
                pw.Text(
                  'الإجمالي: ',
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont('الإجمالي: ', isBold: true),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
                pw.Text(
                  _currencyFormat.format(sale.netAmount),
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont(
                      _currencyFormat.format(sale.netAmount),
                      isBold: true,
                    ),
                  ),
                  textDirection: pw.TextDirection.ltr,
                  textAlign: pw.TextAlign.left,
                ),
              ],
            ),
          ),
        ),

        pw.SizedBox(height: 20),

        // قسم الملاحظات
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey400),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'ملاحظات:',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  font: _getAppropriateFont('ملاحظات:', isBold: true),
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                '- التأكد من استلام جميع بنود الفاتورة',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _getAppropriateFont(
                    '- التأكد من استلام جميع بنود الفاتورة',
                  ),
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'شكراً لتعاملكم معنا',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _getAppropriateFont('شكراً لتعاملكم معنا'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف الإجمالي الجديد
  static pw.Widget _buildTotalRowV2(
    String label,
    double amount, {
    bool isFinal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
              font: _getAppropriateFont(label, isBold: isFinal),
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
              font: _getAppropriateFont(
                _currencyFormat.format(amount),
                isBold: isFinal,
              ),
            ),
            textDirection: pw.TextDirection.ltr,
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة الجديد
  static pw.Widget _buildInvoiceFooterV2() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Align(
        alignment: pw.Alignment.centerRight,
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            font: _getAppropriateFont('شكراً لتعاملكم معنا', isBold: true),
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ),
    );
  }

  /// بناء جدول العناصر الجديد للمشتريات
  static pw.Widget _buildPurchaseItemsTableV2(List<PurchaseItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.8), // القيمة
        1: const pw.FlexColumnWidth(1.5), // السعر
        2: const pw.FlexColumnWidth(1.2), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.8), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2(
              'القيمة',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'السعر',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'الكمية',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
            _buildTableCellV2(
              'البيان',
              isHeader: true,
              textDirection: pw.TextDirection.rtl,
            ),
            _buildTableCellV2(
              'م',
              isHeader: true,
              textDirection: pw.TextDirection.ltr,
            ),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null && item.unit!.isNotEmpty
              ? ' ${item.unit}'
              : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(
                _currencyFormat.format(item.totalPrice),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                _currencyFormat.format(item.unitPrice),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                '${item.quantity}$unitText',
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.ltr,
              ),
              _buildTableCellV2(
                'منتج ${item.productId}',
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
              _buildTableCellV2(
                index.toString(),
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.ltr,
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء إجماليات المشتريات الجديدة
  static pw.Widget _buildPurchaseTotalsV2(Purchase purchase) {
    return pw.Row(
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'ملاحظات:',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont('ملاحظات:', isBold: true),
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  purchase.notes != null && purchase.notes!.isNotEmpty
                      ? purchase.notes!
                      : 'لا توجد ملاحظات.',
                  style: pw.TextStyle(
                    fontSize: 10,
                    font: _getAppropriateFont(
                      purchase.notes != null && purchase.notes!.isNotEmpty
                          ? purchase.notes!
                          : 'لا توجد ملاحظات.',
                    ),
                  ),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.right,
                ),
              ],
            ),
          ),
        ),

        pw.SizedBox(width: 8),
        pw.Expanded(
          flex: 1,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              children: [
                _buildTotalRowV2('الإجمالي:', purchase.totalAmount),
                if (purchase.discountAmount > 0)
                  _buildTotalRowV2('الخصم:', purchase.discountAmount),
                _buildTotalRowV2('الصافي:', purchase.netAmount, isFinal: true),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
