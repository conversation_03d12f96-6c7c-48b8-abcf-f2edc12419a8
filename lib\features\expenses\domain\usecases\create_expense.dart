import '../entities/expense.dart';
import '../repositories/expense_repository.dart';

class CreateExpense {
  final ExpenseRepository _repository;

  CreateExpense(this._repository);

  Future<int> call(Expense expense) async {
    // Validation
    if (expense.description.trim().isEmpty) {
      throw Exception('Expense description cannot be empty');
    }

    if (expense.amount <= 0) {
      throw Exception('Expense amount must be greater than zero');
    }

    if (expense.category.trim().isEmpty) {
      throw Exception('Expense category cannot be empty');
    }

    // Validate expense date is not in the future
    if (expense.expenseDate.isAfter(DateTime.now())) {
      throw Exception('Expense date cannot be in the future');
    }

    try {
      return await _repository.createExpense(expense);
    } catch (e) {
      throw Exception('Failed to create expense: $e');
    }
  }
}
