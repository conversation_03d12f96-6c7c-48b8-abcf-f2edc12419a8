#!/bin/bash

# Market App Documentation PDF Converter
# =====================================
# 
# هذا السكريبت يقوم بتحويل ملف HTML إلى PDF على أنظمة Linux/Mac
# 
# الاستخدام:
# chmod +x convert_to_pdf.sh
# ./convert_to_pdf.sh

# ألوان للإخراج
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# رموز تعبيرية
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
INFO="💡"
FILE="📄"
GLOBE="🌐"
GEAR="🔧"

echo -e "${BLUE}${ROCKET} Market App Documentation PDF Converter${NC}"
echo "=========================================="
echo

# التحقق من وجود ملف HTML
HTML_FILE="Market_App_Documentation.html"
PDF_FILE="Market_App_Documentation.pdf"

if [ ! -f "$HTML_FILE" ]; then
    echo -e "${RED}${CROSS} ملف HTML غير موجود: $HTML_FILE${NC}"
    echo -e "${YELLOW}${INFO} تأكد من وجود الملف في نفس المجلد${NC}"
    exit 1
fi

echo -e "${GREEN}${CHECK} ملف HTML موجود: $HTML_FILE${NC}"
echo

# دالة للبحث عن Chrome/Chromium
find_chrome() {
    local chrome_paths=(
        "google-chrome"
        "chromium-browser"
        "chromium"
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        "/opt/google/chrome/chrome"
        "/usr/bin/google-chrome"
        "/usr/bin/chromium-browser"
    )
    
    for path in "${chrome_paths[@]}"; do
        if command -v "$path" &> /dev/null || [ -f "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# دالة للتحويل باستخدام wkhtmltopdf
convert_with_wkhtmltopdf() {
    echo -e "${BLUE}${GEAR} محاولة التحويل باستخدام wkhtmltopdf...${NC}"
    
    if ! command -v wkhtmltopdf &> /dev/null; then
        echo -e "${RED}${CROSS} wkhtmltopdf غير مثبت${NC}"
        return 1
    fi
    
    local output_file="Market_App_Documentation_wkhtmltopdf.pdf"
    
    wkhtmltopdf \
        --page-size A4 \
        --orientation Portrait \
        --margin-top 20mm \
        --margin-right 20mm \
        --margin-bottom 20mm \
        --margin-left 20mm \
        --encoding UTF-8 \
        --print-media-type \
        --disable-smart-shrinking \
        "$HTML_FILE" \
        "$output_file"
    
    if [ -f "$output_file" ]; then
        echo -e "${GREEN}${CHECK} تم إنشاء PDF بنجاح: $output_file${NC}"
        return 0
    else
        echo -e "${RED}${CROSS} فشل في التحويل باستخدام wkhtmltopdf${NC}"
        return 1
    fi
}

# دالة للتحويل باستخدام Chrome/Chromium
convert_with_chrome() {
    echo -e "${BLUE}${GLOBE} محاولة التحويل باستخدام Chrome/Chromium...${NC}"
    
    local chrome_path
    chrome_path=$(find_chrome)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}${CROSS} لم يتم العثور على Chrome/Chromium${NC}"
        return 1
    fi
    
    echo -e "${GREEN}${CHECK} تم العثور على: $chrome_path${NC}"
    
    local output_file="Market_App_Documentation_Chrome.pdf"
    local html_path="file://$(pwd)/$HTML_FILE"
    
    "$chrome_path" \
        --headless \
        --disable-gpu \
        --print-to-pdf="$output_file" \
        --print-to-pdf-no-header \
        --run-all-compositor-stages-before-draw \
        --virtual-time-budget=15000 \
        "$html_path"
    
    # انتظار قصير للتأكد من اكتمال التحويل
    sleep 3
    
    if [ -f "$output_file" ]; then
        echo -e "${GREEN}${CHECK} تم إنشاء PDF بنجاح: $output_file${NC}"
        return 0
    else
        echo -e "${RED}${CROSS} فشل في التحويل باستخدام Chrome${NC}"
        return 1
    fi
}

# دالة للتحويل باستخدام Pandoc
convert_with_pandoc() {
    echo -e "${BLUE}${GEAR} محاولة التحويل باستخدام Pandoc...${NC}"
    
    if ! command -v pandoc &> /dev/null; then
        echo -e "${RED}${CROSS} Pandoc غير مثبت${NC}"
        return 1
    fi
    
    local md_file="Market_App_Complete_Documentation.md"
    local output_file="Market_App_Documentation_Pandoc.pdf"
    
    if [ ! -f "$md_file" ]; then
        echo -e "${RED}${CROSS} ملف Markdown غير موجود: $md_file${NC}"
        return 1
    fi
    
    pandoc "$md_file" \
        -o "$output_file" \
        --pdf-engine=xelatex \
        --variable mainfont="Arial" \
        --variable sansfont="Arial" \
        --variable monofont="Courier New" \
        --variable fontsize=12pt \
        --variable geometry:margin=2cm \
        --toc
    
    if [ -f "$output_file" ]; then
        echo -e "${GREEN}${CHECK} تم إنشاء PDF بنجاح: $output_file${NC}"
        return 0
    else
        echo -e "${RED}${CROSS} فشل في التحويل باستخدام Pandoc${NC}"
        return 1
    fi
}

# دالة لعرض الطريقة اليدوية
show_manual_method() {
    echo
    echo -e "${YELLOW}📋 طريقة التحويل اليدوية:${NC}"
    echo "================================"
    echo "1. افتح ملف $HTML_FILE في أي متصفح"
    echo "2. اضغط Ctrl+P (أو Cmd+P على Mac)"
    echo "3. اختر 'Save as PDF' من خيارات الطابعة"
    echo "4. تأكد من تفعيل 'Background graphics'"
    echo "5. احفظ الملف باسم Market_App_Documentation.pdf"
    echo
    
    # محاولة فتح الملف في المتصفح الافتراضي
    if command -v xdg-open &> /dev/null; then
        echo -e "${BLUE}${GLOBE} فتح الملف في المتصفح الافتراضي...${NC}"
        xdg-open "$HTML_FILE"
    elif command -v open &> /dev/null; then
        echo -e "${BLUE}${GLOBE} فتح الملف في المتصفح الافتراضي...${NC}"
        open "$HTML_FILE"
    fi
}

# دالة لعرض الملفات المتوفرة
show_available_files() {
    echo
    echo -e "${BLUE}📋 ملفات التوثيق المتوفرة:${NC}"
    for file in Market_App_Documentation*; do
        if [ -f "$file" ]; then
            local size=$(du -h "$file" | cut -f1)
            echo "   ${FILE} $file ($size)"
        fi
    done
}

# دالة لعرض تعليمات التثبيت
show_installation_help() {
    echo
    echo -e "${YELLOW}${INFO} تعليمات التثبيت:${NC}"
    echo
    echo "Ubuntu/Debian:"
    echo "  sudo apt update"
    echo "  sudo apt install wkhtmltopdf google-chrome-stable"
    echo
    echo "CentOS/RHEL/Fedora:"
    echo "  sudo yum install wkhtmltopdf"
    echo "  # أو تحميل Chrome من الموقع الرسمي"
    echo
    echo "macOS (باستخدام Homebrew):"
    echo "  brew install wkhtmltopdf"
    echo "  brew install --cask google-chrome"
    echo
    echo "Arch Linux:"
    echo "  sudo pacman -S wkhtmltopdf google-chrome"
}

# البدء في عملية التحويل
echo -e "${BLUE}🔄 محاولة طرق التحويل المختلفة...${NC}"
echo

success=false

# محاولة التحويل بطرق مختلفة
if convert_with_chrome; then
    success=true
elif convert_with_wkhtmltopdf; then
    success=true
elif convert_with_pandoc; then
    success=true
fi

# النتيجة النهائية
echo
if [ "$success" = true ]; then
    echo -e "${GREEN}🎉 تم التحويل بنجاح!${NC}"
    show_available_files
else
    echo -e "${RED}${CROSS} فشل في جميع طرق التحويل${NC}"
    show_manual_method
    show_installation_help
fi

echo
echo -e "${YELLOW}📞 في حالة وجود مشاكل:${NC}"
echo "   - تأكد من تثبيت إحدى الأدوات المطلوبة"
echo "   - جرب فتح الملف في متصفح آخر"
echo "   - استخدم الطريقة اليدوية المذكورة أعلاه"
echo

exit 0
