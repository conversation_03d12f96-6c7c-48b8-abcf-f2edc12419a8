import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/inventory_count_provider.dart';
import '../../../products/domain/entities/product.dart';
import '../../../../shared_widgets/wrappers.dart';

class InventoryCountFormScreen extends StatefulWidget {
  const InventoryCountFormScreen({super.key});

  @override
  State<InventoryCountFormScreen> createState() =>
      _InventoryCountFormScreenState();
}

class _InventoryCountFormScreenState extends State<InventoryCountFormScreen> {
  final _notesController = TextEditingController();
  final _scrollController = ScrollController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadProductsForCount();
      }
    });
  }

  Future<void> _loadProductsForCount() async {
    try {
      final provider = context.read<InventoryCountProvider>();
      await provider.loadProductsForCount();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنتجات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'جرد المخزون',
      child: Consumer<InventoryCountProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: TextStyle(fontSize: 16, color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadProductsForCount(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.productsForCount.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد منتجات للجرد',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يرجى إضافة منتجات أولاً لتتمكن من إجراء الجرد',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _loadProductsForCount(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          final filteredProducts = _getFilteredProducts(
            provider.productsForCount,
          );

          return Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث عن منتج...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),

              // Products list
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredProducts.length,
                  itemBuilder: (context, index) {
                    final product = filteredProducts[index];
                    return _buildProductCard(context, provider, product);
                  },
                ),
              ),

              // Notes and save button
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(top: BorderSide(color: Colors.grey[300]!)),
                ),
                child: Column(
                  children: [
                    TextField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText: 'ملاحظات (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: provider.isLoading
                            ? null
                            : () => _saveInventoryCount(context, provider),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: provider.isLoading
                            ? const CircularProgressIndicator(
                                color: Colors.white,
                              )
                            : const Text(
                                'حفظ الجرد وتسوية المخزون',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  List<Product> _getFilteredProducts(List<Product> products) {
    if (_searchQuery.isEmpty) return products;

    return products.where((product) {
      return product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (product.barcode?.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ??
              false);
    }).toList();
  }

  Widget _buildProductCard(
    BuildContext context,
    InventoryCountProvider provider,
    Product product,
  ) {
    // التحقق من صحة بيانات المنتج
    if (product.id == null) {
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'خطأ: منتج بدون معرف - ${product.name}',
            style: TextStyle(color: Colors.red[700]),
          ),
        ),
      );
    }

    final warehouseDifference = provider.getWarehouseDifference(product.id!);
    final storeDifference = provider.getStoreDifference(product.id!);
    final hasAnyDifference = provider.hasAnyDifference(product.id!);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: hasAnyDifference ? 3 : 1,
      color: hasAnyDifference ? Colors.orange[50] : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (hasAnyDifference)
                  Icon(Icons.warning, color: Colors.orange[700], size: 20),
              ],
            ),
            if (product.barcode != null && product.barcode!.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'الباركود: ${product.barcode}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
            const SizedBox(height: 12),

            // Warehouse quantity
            _buildQuantityRow(
              context,
              provider,
              product,
              'المخزن الرئيسي',
              product.warehouseQuantity,
              provider.countedWarehouseQuantities[product.id!] ??
                  product.warehouseQuantity,
              warehouseDifference,
              (value) =>
                  provider.updateCountedWarehouseQuantity(product.id!, value),
            ),

            const SizedBox(height: 8),

            // Store quantity
            _buildQuantityRow(
              context,
              provider,
              product,
              'البقالة',
              product.storeQuantity,
              provider.countedStoreQuantities[product.id!] ??
                  product.storeQuantity,
              storeDifference,
              (value) =>
                  provider.updateCountedStoreQuantity(product.id!, value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityRow(
    BuildContext context,
    InventoryCountProvider provider,
    Product product,
    String title,
    int systemQuantity,
    int countedQuantity,
    int difference,
    Function(int) onQuantityChanged,
  ) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(title, style: const TextStyle(fontSize: 14)),
        ),
        Expanded(
          child: Text(
            'النظام: $systemQuantity',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ),
        Expanded(
          child: SizedBox(
            height: 40,
            child: TextFormField(
              initialValue: countedQuantity.toString(),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'الجرد',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 12),
              onChanged: (value) {
                final quantity = int.tryParse(value) ?? 0;
                onQuantityChanged(quantity);
              },
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 60,
          child: Text(
            difference == 0
                ? '✓'
                : (difference > 0 ? '+$difference' : '$difference'),
            style: TextStyle(
              fontSize: 12,
              color: difference == 0
                  ? Colors.green
                  : (difference > 0 ? Colors.blue : Colors.red),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Future<void> _saveInventoryCount(
    BuildContext context,
    InventoryCountProvider provider,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    final success = await provider.createInventoryCount(
      notes: _notesController.text.trim(),
    );

    if (success && mounted) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الجرد وتسوية المخزون بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      navigator.pop();
    }
  }
}
