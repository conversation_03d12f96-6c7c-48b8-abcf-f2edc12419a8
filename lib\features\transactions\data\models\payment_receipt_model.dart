import '../../../transactions/domain/entities/payment_receipt.dart';

class PaymentReceiptModel extends PaymentReceipt {
  const PaymentReceiptModel({
    super.id,
    super.relatedEntityId,
    required super.relatedEntityType,
    required super.transactionDate,
    required super.amount,
    required super.type,
    required super.paymentMethod,
    super.description,
    super.relatedInvoiceId,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create PaymentReceiptModel from PaymentReceipt entity
  factory PaymentReceiptModel.fromEntity(PaymentReceipt entity) {
    return PaymentReceiptModel(
      id: entity.id,
      relatedEntityId: entity.relatedEntityId,
      relatedEntityType: entity.relatedEntityType,
      transactionDate: entity.transactionDate,
      amount: entity.amount,
      type: entity.type,
      paymentMethod: entity.paymentMethod,
      description: entity.description,
      relatedInvoiceId: entity.relatedInvoiceId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Create PaymentReceiptModel from database map
  factory PaymentReceiptModel.fromMap(Map<String, dynamic> map) {
    return PaymentReceiptModel(
      id: map['id'] as int?,
      relatedEntityId: map['relatedEntityId'] as int?,
      relatedEntityType: map['relatedEntityType'] as String,
      transactionDate: DateTime.parse(map['transactionDate'] as String),
      amount: (map['amount'] as num).toDouble(),
      type: map['type'] as String,
      paymentMethod: map['paymentMethod'] as String,
      description: map['description'] as String?,
      relatedInvoiceId: map['relatedInvoiceId'] as int?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// Convert PaymentReceiptModel to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'relatedEntityId': relatedEntityId,
      'relatedEntityType': relatedEntityType,
      'transactionDate': transactionDate.toIso8601String(),
      'amount': amount,
      'type': type,
      'paymentMethod': paymentMethod,
      'description': description,
      'relatedInvoiceId': relatedInvoiceId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  @override
  PaymentReceiptModel copyWith({
    int? id,
    int? relatedEntityId,
    String? relatedEntityType,
    DateTime? transactionDate,
    double? amount,
    String? type,
    String? paymentMethod,
    String? description,
    int? relatedInvoiceId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentReceiptModel(
      id: id ?? this.id,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      transactionDate: transactionDate ?? this.transactionDate,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to PaymentReceipt entity
  PaymentReceipt toEntity() {
    return PaymentReceipt(
      id: id,
      relatedEntityId: relatedEntityId,
      relatedEntityType: relatedEntityType,
      transactionDate: transactionDate,
      amount: amount,
      type: type,
      paymentMethod: paymentMethod,
      description: description,
      relatedInvoiceId: relatedInvoiceId,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
