import '../repositories/purchase_batch_repository.dart';

class UpdatePurchaseBatchQuantityUseCase {
  final PurchaseBatchRepository _repository;

  UpdatePurchaseBatchQuantityUseCase(this._repository);

  Future<void> call(
    int batchId,
    int newRemainingQuantity, {
    bool? isActive,
  }) async {
    // Validation
    if (batchId <= 0) {
      throw Exception('Batch ID must be greater than 0');
    }

    if (newRemainingQuantity < 0) {
      throw Exception('Remaining quantity cannot be negative');
    }

    try {
      await _repository.updateBatchRemainingQuantity(
        batchId,
        newRemainingQuantity,
        isActive: isActive,
      );
    } catch (e) {
      throw Exception('Failed to update purchase batch quantity: $e');
    }
  }
}
