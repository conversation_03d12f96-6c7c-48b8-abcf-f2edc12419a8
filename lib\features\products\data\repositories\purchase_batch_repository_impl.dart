import '../../domain/entities/purchase_batch.dart';
import '../../domain/repositories/purchase_batch_repository.dart';
import '../datasources/purchase_batch_database_service.dart';
import '../models/purchase_batch_model.dart';

class PurchaseBatchRepositoryImpl implements PurchaseBatchRepository {
  final PurchaseBatchDatabaseService _databaseService;

  PurchaseBatchRepositoryImpl(this._databaseService);

  @override
  Future<int> addBatch(PurchaseBatch batch) async {
    try {
      final batchModel = PurchaseBatchModel(
        id: batch.id,
        productId: batch.productId,
        purchaseDate: batch.purchaseDate,
        quantity: batch.quantity,
        unitPurchasePrice: batch.unitPurchasePrice,
        remainingQuantity: batch.remainingQuantity,
        isActive: batch.isActive,
      );

      return await _databaseService.addBatch(batchModel);
    } catch (e) {
      throw Exception('Failed to add batch: $e');
    }
  }

  @override
  Future<List<PurchaseBatch>> getOldestActiveBatches(int productId) async {
    try {
      final batchModels = await _databaseService.getOldestActiveBatches(
        productId,
      );
      return batchModels
          .map((model) => PurchaseBatch.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get oldest active batches: $e');
    }
  }

  @override
  Future<void> updateBatchRemainingQuantity(
    int batchId,
    int newRemainingQuantity, {
    bool? isActive,
  }) async {
    try {
      await _databaseService.updateBatchRemainingQuantity(
        batchId,
        newRemainingQuantity,
        isActive: isActive,
      );
    } catch (e) {
      throw Exception('Failed to update batch remaining quantity: $e');
    }
  }

  @override
  Future<PurchaseBatch?> getBatchById(int id) async {
    try {
      final batchModel = await _databaseService.getBatchById(id);
      return batchModel != null ? PurchaseBatch.fromModel(batchModel) : null;
    } catch (e) {
      throw Exception('Failed to get batch by id: $e');
    }
  }

  @override
  Future<List<PurchaseBatch>> getAllBatchesForProduct(int productId) async {
    try {
      final batchModels = await _databaseService.getAllBatchesForProduct(
        productId,
      );
      return batchModels
          .map((model) => PurchaseBatch.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get all batches for product: $e');
    }
  }

  @override
  Future<void> deleteBatchesForProduct(int productId) async {
    try {
      await _databaseService.deleteBatchesForProduct(productId);
    } catch (e) {
      throw Exception('Failed to delete batches for product: $e');
    }
  }

  @override
  Future<int> getTotalRemainingQuantity(int productId) async {
    try {
      return await _databaseService.getTotalRemainingQuantity(productId);
    } catch (e) {
      throw Exception('Failed to get total remaining quantity: $e');
    }
  }
}
