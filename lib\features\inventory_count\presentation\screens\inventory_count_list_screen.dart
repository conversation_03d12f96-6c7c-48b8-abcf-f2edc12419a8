import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../providers/inventory_count_provider.dart';
import '../../domain/entities/inventory_count.dart';

class InventoryCountListScreen extends StatefulWidget {
  const InventoryCountListScreen({super.key});

  @override
  State<InventoryCountListScreen> createState() =>
      _InventoryCountListScreenState();
}

class _InventoryCountListScreenState extends State<InventoryCountListScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<InventoryCountProvider>().fetchInventoryCounts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجلات الجرد'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/inventory_count/new'),
          ),
        ],
      ),
      body: Consumer<InventoryCountProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: TextStyle(fontSize: 16, color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.fetchInventoryCounts(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.inventoryCounts.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد سجلات جرد',
                    style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على + لإنشاء جرد جديد',
                    style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => provider.fetchInventoryCounts(),
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: provider.inventoryCounts.length,
              itemBuilder: (context, index) {
                final inventoryCount = provider.inventoryCounts[index];
                return _buildInventoryCountCard(context, inventoryCount);
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/inventory_count/new'),
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildInventoryCountCard(
    BuildContext context,
    InventoryCount inventoryCount,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => context.push('/inventory_count/${inventoryCount.id}'),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'جرد رقم ${inventoryCount.id}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: inventoryCount.hasAnyDifference
                          ? Colors.orange[100]
                          : Colors.green[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      inventoryCount.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        color: inventoryCount.hasAnyDifference
                            ? Colors.orange[800]
                            : Colors.green[800],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'تاريخ الجرد: ${inventoryCount.formattedCountDate}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildQuantityInfo(
                      'المخزن الرئيسي',
                      inventoryCount.systemWarehouseQuantity,
                      inventoryCount.countedWarehouseQuantity,
                      inventoryCount.warehouseDifference,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildQuantityInfo(
                      'البقالة',
                      inventoryCount.systemStoreQuantity,
                      inventoryCount.countedStoreQuantity,
                      inventoryCount.storeDifference,
                    ),
                  ),
                ],
              ),
              if (inventoryCount.notes != null &&
                  inventoryCount.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'ملاحظات: ${inventoryCount.notes}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuantityInfo(
    String title,
    int systemQty,
    int countedQty,
    int difference,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Text(
          'النظام: $systemQty',
          style: TextStyle(fontSize: 11, color: Colors.grey[600]),
        ),
        Text(
          'الجرد: $countedQty',
          style: TextStyle(fontSize: 11, color: Colors.grey[600]),
        ),
        if (difference != 0)
          Text(
            difference > 0 ? '+$difference' : '$difference',
            style: TextStyle(
              fontSize: 11,
              color: difference > 0 ? Colors.green[700] : Colors.red[700],
              fontWeight: FontWeight.bold,
            ),
          ),
      ],
    );
  }
}
