import 'package:sqflite/sqflite.dart';
import 'package:market/core/database/database_service.dart';
import 'package:market/features/products/data/models/product_model.dart';

class ProductDatabaseService {
  static const String tableName = 'products';

  Future<Database> get _database async {
    return await DatabaseService.instance.database;
  }

  // Create a new product
  Future<int> createProduct(ProductModel product) async {
    try {
      final db = await _database;
      final productMap = product.toMap();
      productMap.remove('id'); // Remove id for auto-increment

      final id = await db.insert(
        tableName,
        productMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return id;
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  // Get all products
  Future<List<ProductModel>> getProducts() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ProductModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get products: $e');
    }
  }

  // Get product by ID
  Future<ProductModel?> getProductById(int id) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return ProductModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product by id: $e');
    }
  }

  // Update product
  Future<int> updateProduct(ProductModel product) async {
    try {
      final db = await _database;
      final result = await db.update(
        tableName,
        product.toMap(),
        where: 'id = ?',
        whereArgs: [product.id],
      );

      return result;
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product
  Future<int> deleteProduct(int id) async {
    try {
      final db = await _database;
      final result = await db.delete(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
      );

      return result;
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  // Search products by name or barcode
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'name LIKE ? OR barcode LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ProductModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  // Get products by category
  Future<List<ProductModel>> getProductsByCategory(String category) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'category = ?',
        whereArgs: [category],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ProductModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get products by category: $e');
    }
  }

  // Get low stock products
  Future<List<ProductModel>> getLowStockProducts() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: '(warehouseQuantity + storeQuantity) <= minStockQuantity',
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ProductModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get low stock products: $e');
    }
  }
}
