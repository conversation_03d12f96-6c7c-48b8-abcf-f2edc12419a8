import '../repositories/order_repository.dart';

class DeleteOrderUseCase {
  final OrderRepository _repository;

  DeleteOrderUseCase(this._repository);

  Future<void> call(int id) async {
    if (id <= 0) {
      throw Exception('Order ID must be greater than 0');
    }

    try {
      await _repository.deleteOrder(id);
    } catch (e) {
      throw Exception('Failed to delete order: $e');
    }
  }
}
