import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../../shared_widgets/wrappers.dart';
import '../../domain/entities/payment_receipt.dart';
import '../providers/payment_receipt_provider.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../suppliers/presentation/providers/supplier_provider.dart';

/// شاشة عرض تفاصيل سند قبض أو دفع
class PaymentReceiptDetailsScreen extends StatefulWidget {
  final String receiptId;

  const PaymentReceiptDetailsScreen({super.key, required this.receiptId});

  @override
  State<PaymentReceiptDetailsScreen> createState() =>
      _PaymentReceiptDetailsScreenState();
}

class _PaymentReceiptDetailsScreenState
    extends State<PaymentReceiptDetailsScreen> {
  @override
  void initState() {
    super.initState();
    _loadReceiptDetails();
  }

  /// تحميل تفاصيل السند
  Future<void> _loadReceiptDetails() async {
    final receiptId = int.tryParse(widget.receiptId);
    if (receiptId == null) {
      _showErrorSnackBar('معرف السند غير صحيح');
      return;
    }

    final paymentReceiptProvider = Provider.of<PaymentReceiptProvider>(
      context,
      listen: false,
    );
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final supplierProvider = Provider.of<SupplierProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      paymentReceiptProvider.getPaymentReceiptById(receiptId),
      customerProvider.getAllCustomers(),
      supplierProvider.getAllSuppliers(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<
      PaymentReceiptProvider,
      CustomerProvider,
      SupplierProvider
    >(
      builder:
          (
            context,
            paymentReceiptProvider,
            customerProvider,
            supplierProvider,
            child,
          ) {
            if (paymentReceiptProvider.isLoading) {
              return const SecondaryScreenWrapper(
                title: 'تفاصيل السند',
                child: Center(child: CircularProgressIndicator()),
              );
            }

            if (paymentReceiptProvider.errorMessage != null) {
              return SecondaryScreenWrapper(
                title: 'تفاصيل السند',
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red[300]),
                      const SizedBox(height: 16),
                      Text(
                        paymentReceiptProvider.errorMessage!,
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadReceiptDetails,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              );
            }

            final receipt = paymentReceiptProvider.selectedPaymentReceipt;
            if (receipt == null) {
              return const SecondaryScreenWrapper(
                title: 'تفاصيل السند',
                child: Center(
                  child: Text(
                    'السند غير موجود',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              );
            }

            return SecondaryScreenWrapper(
              title: 'تفاصيل السند',
              child: _buildReceiptDetails(
                receipt,
                customerProvider,
                supplierProvider,
              ),
            );
          },
    );
  }

  /// بناء تفاصيل السند
  Widget _buildReceiptDetails(
    PaymentReceipt receipt,
    CustomerProvider customerProvider,
    SupplierProvider supplierProvider,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildReceiptHeader(receipt),
          const SizedBox(height: 16),
          _buildReceiptInfo(receipt, customerProvider, supplierProvider),
          const SizedBox(height: 16),
          _buildAmountSection(receipt),
          const SizedBox(height: 16),
          _buildPaymentMethodSection(receipt),
          if (receipt.relatedInvoiceId != null) ...[
            const SizedBox(height: 16),
            _buildInvoiceSection(receipt),
          ],
          if (receipt.description != null &&
              receipt.description!.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildDescriptionSection(receipt),
          ],
          const SizedBox(height: 16),
          _buildTimestampSection(receipt),
          const SizedBox(height: 32),
          _buildActionButtons(receipt),
        ],
      ),
    );
  }

  /// رأس السند
  Widget _buildReceiptHeader(PaymentReceipt receipt) {
    return Card(
      color: receipt.type == 'payment_in' ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: receipt.type == 'payment_in'
                  ? Colors.green
                  : Colors.red,
              child: Icon(
                receipt.type == 'payment_in'
                    ? Icons.arrow_downward
                    : Icons.arrow_upward,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              receipt.typeDisplayName,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'سند رقم ${receipt.id}',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  /// معلومات السند الأساسية
  Widget _buildReceiptInfo(
    PaymentReceipt receipt,
    CustomerProvider customerProvider,
    SupplierProvider supplierProvider,
  ) {
    // الحصول على اسم الكيان المرتبط
    String entityName = 'غير محدد';
    String entityType = receipt.relatedEntityType == 'customer'
        ? 'العميل'
        : 'المورد';

    if (receipt.relatedEntityId != null) {
      if (receipt.relatedEntityType == 'customer') {
        final customer = customerProvider.customers
            .where((c) => c.id == receipt.relatedEntityId)
            .firstOrNull;
        entityName = customer?.name ?? 'عميل غير موجود';
      } else if (receipt.relatedEntityType == 'supplier') {
        final supplier = supplierProvider.suppliers
            .where((s) => s.id == receipt.relatedEntityId)
            .firstOrNull;
        entityName = supplier?.name ?? 'مورد غير موجود';
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات السند',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'التاريخ',
              DateFormat('yyyy/MM/dd - HH:mm').format(receipt.transactionDate),
            ),
            _buildInfoRow(entityType, entityName),
            _buildInfoRow('نوع المعاملة', receipt.typeDisplayName),
          ],
        ),
      ),
    );
  }

  /// قسم المبلغ
  Widget _buildAmountSection(PaymentReceipt receipt) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المبلغ',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: receipt.type == 'payment_in'
                      ? Colors.green[100]
                      : Colors.red[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: receipt.type == 'payment_in'
                        ? Colors.green
                        : Colors.red,
                    width: 2,
                  ),
                ),
                child: Text(
                  '${receipt.amount.toStringAsFixed(2)} ر.ي',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: receipt.type == 'payment_in'
                        ? Colors.green[800]
                        : Colors.red[800],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم طريقة الدفع
  Widget _buildPaymentMethodSection(PaymentReceipt receipt) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'طريقة الدفع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    _getPaymentMethodIcon(receipt.paymentMethod),
                    color: Colors.blue[700],
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    receipt.paymentMethod,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الفاتورة المرتبطة
  Widget _buildInvoiceSection(PaymentReceipt receipt) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفاتورة المرتبطة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.receipt, color: Colors.orange[700], size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'فاتورة رقم ${receipt.relatedInvoiceId}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.orange[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الوصف
  Widget _buildDescriptionSection(PaymentReceipt receipt) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الوصف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                receipt.description!,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الطوابع الزمنية
  Widget _buildTimestampSection(PaymentReceipt receipt) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات إضافية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'تاريخ الإنشاء',
              DateFormat('yyyy/MM/dd - HH:mm:ss').format(receipt.createdAt),
            ),
            _buildInfoRow(
              'آخر تحديث',
              DateFormat('yyyy/MM/dd - HH:mm:ss').format(receipt.updatedAt),
            ),
          ],
        ),
      ),
    );
  }

  /// أزرار الإجراءات
  Widget _buildActionButtons(PaymentReceipt receipt) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              // TODO: تطبيق تعديل السند
              _showComingSoonSnackBar('تعديل السند');
            },
            icon: const Icon(Icons.edit),
            label: const Text('تعديل'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              // TODO: تطبيق حذف السند
              _showDeleteConfirmation(receipt);
            },
            icon: const Icon(Icons.delete),
            label: const Text('حذف'),
            style: OutlinedButton.styleFrom(foregroundColor: Colors.red),
          ),
        ),
      ],
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  /// الحصول على أيقونة طريقة الدفع
  IconData _getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'نقدي':
        return Icons.money;
      case 'تحويل بنكي':
        return Icons.account_balance;
      case 'شيك':
        return Icons.receipt_long;
      case 'بطاقة ائتمان':
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(PaymentReceipt receipt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف ${receipt.typeDisplayName} رقم ${receipt.id}؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showComingSoonSnackBar('حذف السند');
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة "قريباً"
  void _showComingSoonSnackBar(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
