import '../entities/sale.dart';
import '../repositories/sale_repository.dart';

class UpdateSaleUseCase {
  final SaleRepository _repository;

  UpdateSaleUseCase(this._repository);

  Future<void> call(Sale sale) async {
    // Validation
    if (sale.id == null || sale.id! <= 0) {
      throw Exception('Sale ID must be provided and greater than 0');
    }

    if (sale.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (sale.totalPaidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (sale.totalPaidAmount > sale.totalAmount) {
      throw Exception('Paid amount cannot exceed total amount');
    }

    if (sale.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (sale.status.isEmpty) {
      throw Exception('Sale status cannot be empty');
    }

    // Validate payment amounts - dueAmount is now calculated automatically
    // No need for additional validation since dueAmount = totalAmount - totalPaidAmount

    try {
      await _repository.updateSale(sale);
    } catch (e) {
      throw Exception('Failed to update sale: $e');
    }
  }
}
