import '../entities/purchase_batch.dart';

abstract class PurchaseBatchRepository {
  /// Add a new purchase batch
  Future<int> addBatch(PurchaseBatch batch);

  /// Get oldest active batches for a product (FIFO order)
  Future<List<PurchaseBatch>> getOldestActiveBatches(int productId);

  /// Update remaining quantity of a batch and optionally its active status
  Future<void> updateBatchRemainingQuantity(
    int batchId,
    int newRemainingQuantity, {
    bool? isActive,
  });

  /// Get a specific batch by ID
  Future<PurchaseBatch?> getBatchById(int id);

  /// Get all batches for a product (for debugging/reporting)
  Future<List<PurchaseBatch>> getAllBatchesForProduct(int productId);

  /// Delete all batches for a product (used when product is deleted)
  Future<void> deleteBatchesForProduct(int productId);

  /// Get total remaining quantity for a product across all active batches
  Future<int> getTotalRemainingQuantity(int productId);
}
