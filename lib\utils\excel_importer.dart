import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:excel/excel.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/customers/domain/entities/customer.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';

class ExcelImporter {
  static const String _productsSheetName = 'Products';
  static const String _customersSheetName = 'Customers';
  static const String _suppliersSheetName = 'Suppliers';

  /// Import products from Excel file
  /// Expected columns: Name, Barcode, WholesalePrice, RetailPrice, LastPurchasePrice,
  /// WarehouseQuantity, StoreQuantity, MinStockLevel, CategoryId, Description
  static Future<List<Product>> importProducts(File excelFile) async {
    try {
      final bytes = await excelFile.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      Sheet? sheet =
          excel.tables[_productsSheetName] ?? excel.tables.values.firstOrNull;

      if (sheet == null) {
        throw Exception('لا يمكن العثور على ورقة البيانات');
      }

      final List<Product> products = [];
      final rows = sheet.rows;

      if (rows.isEmpty) {
        throw Exception('ملف Excel فارغ');
      }

      // Skip header row (first row)
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.isEmpty || _isRowEmpty(row)) continue;

        try {
          final product = _parseProductRow(row, i + 1);
          if (product != null) {
            products.add(product);
          }
        } catch (e) {
          debugPrint('خطأ في الصف ${i + 1}: $e');
          // Continue with next row instead of stopping
        }
      }

      return products;
    } catch (e) {
      throw Exception('فشل في استيراد المنتجات: $e');
    }
  }

  /// Import customers from Excel file
  /// Expected columns: Name, Phone, Email, Address, CreditLimit, Notes
  static Future<List<Customer>> importCustomers(File excelFile) async {
    try {
      final bytes = await excelFile.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      Sheet? sheet =
          excel.tables[_customersSheetName] ?? excel.tables.values.firstOrNull;

      if (sheet == null) {
        throw Exception('لا يمكن العثور على ورقة البيانات');
      }

      final List<Customer> customers = [];
      final rows = sheet.rows;

      if (rows.isEmpty) {
        throw Exception('ملف Excel فارغ');
      }

      // Skip header row (first row)
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.isEmpty || _isRowEmpty(row)) continue;

        try {
          final customer = _parseCustomerRow(row, i + 1);
          if (customer != null) {
            customers.add(customer);
          }
        } catch (e) {
          debugPrint('خطأ في الصف ${i + 1}: $e');
          // Continue with next row instead of stopping
        }
      }

      return customers;
    } catch (e) {
      throw Exception('فشل في استيراد العملاء: $e');
    }
  }

  /// Import suppliers from Excel file
  /// Expected columns: Name, Phone, Email, Address, ContactPerson, Notes
  static Future<List<Supplier>> importSuppliers(File excelFile) async {
    try {
      final bytes = await excelFile.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      Sheet? sheet =
          excel.tables[_suppliersSheetName] ?? excel.tables.values.firstOrNull;

      if (sheet == null) {
        throw Exception('لا يمكن العثور على ورقة البيانات');
      }

      final List<Supplier> suppliers = [];
      final rows = sheet.rows;

      if (rows.isEmpty) {
        throw Exception('ملف Excel فارغ');
      }

      // Skip header row (first row)
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.isEmpty || _isRowEmpty(row)) continue;

        try {
          final supplier = _parseSupplierRow(row, i + 1);
          if (supplier != null) {
            suppliers.add(supplier);
          }
        } catch (e) {
          debugPrint('خطأ في الصف ${i + 1}: $e');
          // Continue with next row instead of stopping
        }
      }

      return suppliers;
    } catch (e) {
      throw Exception('فشل في استيراد الموردين: $e');
    }
  }

  static bool _isRowEmpty(List<Data?> row) {
    return row.every(
      (cell) => cell?.value == null || cell!.value.toString().trim().isEmpty,
    );
  }

  static Product? _parseProductRow(List<Data?> row, int rowNumber) {
    try {
      // Expected columns: Name, Category, Unit, Barcode, WholesalePrice, RetailPrice,
      // LastPurchasePrice, WarehouseQuantity, StoreQuantity, MinStockQuantity, Description

      final name = _getCellValue(row, 0)?.toString().trim();
      if (name == null || name.isEmpty) {
        throw Exception('اسم المنتج مطلوب');
      }

      final category = _getCellValue(row, 1)?.toString().trim() ?? 'عام';
      final unit = _getCellValue(row, 2)?.toString().trim() ?? 'قطعة';
      final barcode = _getCellValue(row, 3)?.toString().trim();
      final wholesalePrice = _parseDouble(_getCellValue(row, 4)) ?? 0.0;
      final retailPrice = _parseDouble(_getCellValue(row, 5)) ?? 0.0;
      final lastPurchasePrice = _parseDouble(_getCellValue(row, 6));
      final warehouseQuantity = _parseInt(_getCellValue(row, 7)) ?? 0;
      final storeQuantity = _parseInt(_getCellValue(row, 8)) ?? 0;
      final minStockQuantity = _parseInt(_getCellValue(row, 9)) ?? 0;
      final description = _getCellValue(row, 10)?.toString().trim();

      return Product(
        name: name,
        category: category,
        unit: unit,
        barcode: barcode?.isNotEmpty == true ? barcode : null,
        wholesalePrice: wholesalePrice,
        retailPrice: retailPrice,
        lastPurchasePrice: lastPurchasePrice,
        warehouseQuantity: warehouseQuantity,
        storeQuantity: storeQuantity,
        minStockQuantity: minStockQuantity,
        description: description?.isNotEmpty == true ? description : null,
      );
    } catch (e) {
      throw Exception('خطأ في تحليل بيانات المنتج: $e');
    }
  }

  static Customer? _parseCustomerRow(List<Data?> row, int rowNumber) {
    try {
      // Expected columns: Name, Phone, Email, Address, CreditLimit, Notes

      final name = _getCellValue(row, 0)?.toString().trim();
      if (name == null || name.isEmpty) {
        throw Exception('اسم العميل مطلوب');
      }

      final phone = _getCellValue(row, 1)?.toString().trim();
      final email = _getCellValue(row, 2)?.toString().trim();
      final address = _getCellValue(row, 3)?.toString().trim();
      final creditLimit = _parseDouble(_getCellValue(row, 4)) ?? 0.0;
      final notes = _getCellValue(row, 5)?.toString().trim();

      return Customer(
        name: name,
        phone: phone?.isNotEmpty == true ? phone : null,
        email: email?.isNotEmpty == true ? email : null,
        address: address?.isNotEmpty == true ? address : null,
        creditLimit: creditLimit,
        notes: notes?.isNotEmpty == true ? notes : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('خطأ في تحليل بيانات العميل: $e');
    }
  }

  static Supplier? _parseSupplierRow(List<Data?> row, int rowNumber) {
    try {
      // Expected columns: Name, Phone, Email, Address, ContactPerson, Notes

      final name = _getCellValue(row, 0)?.toString().trim();
      if (name == null || name.isEmpty) {
        throw Exception('اسم المورد مطلوب');
      }

      final phone = _getCellValue(row, 1)?.toString().trim();
      final email = _getCellValue(row, 2)?.toString().trim();
      final address = _getCellValue(row, 3)?.toString().trim();
      final contactPerson = _getCellValue(row, 4)?.toString().trim();
      final notes = _getCellValue(row, 5)?.toString().trim();

      return Supplier(
        name: name,
        phone: phone?.isNotEmpty == true ? phone : null,
        email: email?.isNotEmpty == true ? email : null,
        address: address?.isNotEmpty == true ? address : null,
        contactPerson: contactPerson?.isNotEmpty == true ? contactPerson : null,
        notes: notes?.isNotEmpty == true ? notes : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('خطأ في تحليل بيانات المورد: $e');
    }
  }

  static dynamic _getCellValue(List<Data?> row, int index) {
    if (index >= row.length) return null;
    return row[index]?.value;
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final trimmed = value.trim();
      if (trimmed.isEmpty) return null;
      return double.tryParse(trimmed);
    }
    return null;
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final trimmed = value.trim();
      if (trimmed.isEmpty) return null;
      return int.tryParse(trimmed);
    }
    return null;
  }
}
