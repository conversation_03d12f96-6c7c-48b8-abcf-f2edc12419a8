import 'package:flutter/foundation.dart';
import '../entities/customer_account.dart';
import '../repositories/customer_account_repository.dart';

class AddCustomerAccountEntryUseCase {
  final CustomerAccountRepository _repository;

  AddCustomerAccountEntryUseCase(this._repository);

  Future<int> call(CustomerAccount entry) async {
    // Validation
    if (entry.customerId <= 0) {
      throw Exception('Customer ID must be greater than 0');
    }

    if (entry.type.isEmpty) {
      throw Exception('Transaction type cannot be empty');
    }

    if (entry.amount < 0) {
      throw Exception('Amount cannot be negative');
    }

    // Validate transaction type
    const validTypes = [
      'sale_invoice',
      'retail_debt',
      'payment',
      'payment_in', // سندات القبض من العملاء
      'return',
      'cash_sale_invoice',
    ];
    if (!validTypes.contains(entry.type)) {
      throw Exception('Invalid transaction type: ${entry.type}');
    }

    debugPrint(
      '🔍 DEBUG: AddCustomerAccountEntryUseCase: Calling repository to add entry.',
    );
    try {
      final result = await _repository.addCustomerAccountEntry(entry);
      debugPrint(
        '✅ DEBUG: AddCustomerAccountEntryUseCase: Repository call successful.',
      );
      return result;
    } catch (e) {
      debugPrint(
        '❌ ERROR: AddCustomerAccountEntryUseCase failed: ${e.toString()}',
      );
      throw Exception('Failed to add customer account entry: $e');
    }
  }
}
