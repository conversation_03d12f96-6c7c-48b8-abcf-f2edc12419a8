import '../entities/app_notification.dart';
import '../repositories/notification_repository.dart';

class CreateNotificationUseCase {
  final NotificationRepository _repository;

  CreateNotificationUseCase(this._repository);

  Future<int> call(AppNotification notification) async {
    // Validation
    if (notification.type.isEmpty) {
      throw Exception('Notification type cannot be empty');
    }

    if (notification.message.isEmpty) {
      throw Exception('Notification message cannot be empty');
    }

    try {
      return await _repository.createNotification(notification);
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }
}
