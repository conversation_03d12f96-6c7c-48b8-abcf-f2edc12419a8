import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/customer_model.dart';

class CustomerDatabaseService {
  final DatabaseService _databaseService;

  CustomerDatabaseService(this._databaseService);

  Future<List<CustomerModel>> getAllCustomers() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return CustomerModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get customers: $e');
    }
  }

  Future<CustomerModel?> getCustomerById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return CustomerModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get customer by id: $e');
    }
  }

  Future<int> createCustomer(CustomerModel customer) async {
    try {
      final db = await _databaseService.database;
      final customerMap = customer.toMap();
      customerMap.remove('id'); // Remove id for auto-increment

      return await db.insert(
        'customers',
        customerMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  Future<void> updateCustomer(CustomerModel customer) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'customers',
        customer.toMap(),
        where: 'id = ?',
        whereArgs: [customer.id],
      );
    } catch (e) {
      throw Exception('Failed to update customer: $e');
    }
  }

  Future<void> deleteCustomer(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete('customers', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('Failed to delete customer: $e');
    }
  }

  Future<List<CustomerModel>> searchCustomers(String query) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'name LIKE ? OR phone LIKE ? OR email LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return CustomerModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to search customers: $e');
    }
  }

  Future<void> addAccountEntry(
    int customerId,
    double amount,
    String type,
    String description,
  ) async {
    try {
      final db = await _databaseService.database;
      await db.insert('customer_account_entries', {
        'customer_id': customerId,
        'amount': amount,
        'type': type,
        'description': description,
        'created_at': DateTime.now().toIso8601String(),
      }, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      throw Exception('Failed to add account entry: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getAccountStatement(int customerId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customer_account_entries',
        where: 'customer_id = ?',
        whereArgs: [customerId],
        orderBy: 'created_at DESC',
      );

      return maps;
    } catch (e) {
      throw Exception('Failed to get account statement: $e');
    }
  }
}
