import 'package:flutter/material.dart';
import '../../domain/entities/expense.dart';
import '../../domain/usecases/create_expense.dart';
import '../../domain/usecases/update_expense.dart';
import '../../domain/usecases/get_all_expenses.dart';
import '../../domain/usecases/get_expense_by_id.dart';
import '../../domain/usecases/delete_expense.dart';
import '../../domain/repositories/expense_repository.dart';

class ExpenseProvider extends ChangeNotifier {
  final CreateExpense _createExpenseUseCase;
  final UpdateExpense _updateExpenseUseCase;
  final GetAllExpenses _getAllExpensesUseCase;
  final GetExpenseById _getExpenseByIdUseCase;
  final DeleteExpense _deleteExpenseUseCase;
  final ExpenseRepository _expenseRepository;

  ExpenseProvider(
    this._createExpenseUseCase,
    this._updateExpenseUseCase,
    this._getAllExpensesUseCase,
    this._getExpenseByIdUseCase,
    this._deleteExpenseUseCase,
    this._expenseRepository,
  );

  // State variables
  List<Expense> _expenses = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Filter variables
  String? _selectedCategory;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Getters
  List<Expense> get expenses => _expenses;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get selectedCategory => _selectedCategory;
  DateTime? get selectedFromDate => _selectedFromDate;
  DateTime? get selectedToDate => _selectedToDate;

  // Fetch all expenses
  Future<void> fetchExpenses() async {
    _setLoading(true);
    _clearError();

    try {
      _expenses = await _getAllExpensesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المصروفات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create expense
  Future<bool> createExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _createExpenseUseCase.call(expense);
      await fetchExpenses(); // Refresh the list
      return true;
    } catch (e) {
      _setError('فشل في إضافة المصروف: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update expense
  Future<bool> updateExpense(Expense expense) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateExpenseUseCase.call(expense);
      await fetchExpenses(); // Refresh the list
      return true;
    } catch (e) {
      _setError('فشل في تحديث المصروف: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get expense by ID
  Future<Expense?> getExpenseById(int id) async {
    try {
      return await _getExpenseByIdUseCase.call(id);
    } catch (e) {
      _setError('فشل في جلب المصروف: ${e.toString()}');
      return null;
    }
  }

  // Get expenses by category
  Future<List<Expense>> getExpensesByCategory(String category) async {
    try {
      return await _expenseRepository.getExpensesByCategory(category);
    } catch (e) {
      _setError('فشل في جلب المصروفات حسب الفئة: ${e.toString()}');
      return [];
    }
  }

  // Get expenses by date range
  Future<List<Expense>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _expenseRepository.getExpensesByDateRange(
        startDate,
        endDate,
      );
    } catch (e) {
      _setError('فشل في جلب المصروفات حسب التاريخ: ${e.toString()}');
      return [];
    }
  }

  // Get total expenses by category
  Future<double> getTotalExpensesByCategory(String category) async {
    try {
      return await _expenseRepository.getTotalExpensesByCategory(category);
    } catch (e) {
      _setError('فشل في حساب إجمالي المصروفات: ${e.toString()}');
      return 0.0;
    }
  }

  // Get total expenses by date range
  Future<double> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _expenseRepository.getTotalExpensesByDateRange(
        startDate,
        endDate,
      );
    } catch (e) {
      _setError('فشل في حساب إجمالي المصروفات: ${e.toString()}');
      return 0.0;
    }
  }

  // Get expense categories
  List<String> getExpenseCategories() {
    final categories = _expenses.map((e) => e.category).toSet().toList();
    categories.sort();
    return categories;
  }

  // Search expenses
  List<Expense> searchExpenses(String query) {
    if (query.isEmpty) return _expenses;

    return _expenses.where((expense) {
      return expense.description.toLowerCase().contains(query.toLowerCase()) ||
          expense.category.toLowerCase().contains(query.toLowerCase()) ||
          (expense.notes?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  // Filter expenses
  void filterExpenses({
    String? category,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    _selectedCategory = category;
    _selectedFromDate = fromDate;
    _selectedToDate = toDate;
    notifyListeners();
  }

  // Get filtered expenses (by all filters and search)
  List<Expense> getFilteredExpenses(String searchQuery) {
    List<Expense> filteredExpenses = _expenses;

    // Apply category filter
    if (_selectedCategory != null && _selectedCategory!.isNotEmpty) {
      filteredExpenses = filteredExpenses
          .where((expense) => expense.category == _selectedCategory)
          .toList();
    }

    // Apply date range filter
    if (_selectedFromDate != null) {
      filteredExpenses = filteredExpenses
          .where(
            (expense) =>
                expense.expenseDate.isAfter(_selectedFromDate!) ||
                expense.expenseDate.isAtSameMomentAs(_selectedFromDate!),
          )
          .toList();
    }
    if (_selectedToDate != null) {
      final endOfDay = DateTime(
        _selectedToDate!.year,
        _selectedToDate!.month,
        _selectedToDate!.day,
        23,
        59,
        59,
      );
      filteredExpenses = filteredExpenses
          .where(
            (expense) =>
                expense.expenseDate.isBefore(endOfDay) ||
                expense.expenseDate.isAtSameMomentAs(endOfDay),
          )
          .toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredExpenses = filteredExpenses.where((expense) {
        return expense.description.toLowerCase().contains(
              searchQuery.toLowerCase(),
            ) ||
            expense.category.toLowerCase().contains(
              searchQuery.toLowerCase(),
            ) ||
            (expense.notes?.toLowerCase().contains(searchQuery.toLowerCase()) ??
                false);
      }).toList();
    }

    return filteredExpenses;
  }

  // Clear all filters
  void clearFilters() {
    _selectedCategory = null;
    _selectedFromDate = null;
    _selectedToDate = null;
    notifyListeners();
  }

  // Get unique categories for filter dropdown
  List<String> getUniqueCategories() {
    final categories = _expenses
        .map((expense) => expense.category)
        .toSet()
        .toList();
    categories.sort();
    return categories;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Delete expense
  Future<void> deleteExpense(int expenseId) async {
    try {
      await _deleteExpenseUseCase.call(expenseId);
      await fetchExpenses(); // Refresh the list
    } catch (e) {
      _setError('فشل في حذف المصروف: ${e.toString()}');
      rethrow;
    }
  }

  // Clear expenses list
  void clearExpenses() {
    _expenses.clear();
    notifyListeners();
  }

  // Initialize - fetch expenses
  Future<void> initialize() async {
    await fetchExpenses();
  }
}
