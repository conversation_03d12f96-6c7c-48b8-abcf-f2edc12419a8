import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/repositories/product_repository.dart';
import 'package:market/features/products/domain/usecases/create_product.dart';

import 'create_product_use_case_test.mocks.dart';

@GenerateMocks([ProductRepository])
void main() {
  late CreateProductUseCase useCase;
  late MockProductRepository mockProductRepository;

  setUp(() {
    mockProductRepository = MockProductRepository();
    useCase = CreateProductUseCase(mockProductRepository);
  });

  group('CreateProductUseCase', () {
    const testProduct = Product(
      name: 'منتج تجريبي',
      description: 'وصف المنتج التجريبي',
      category: 'إلكترونيات',
      unit: 'قطعة',
      lastPurchasePrice: 100.0,
      wholesalePrice: 120.0,
      retailPrice: 150.0,
      minStockQuantity: 10,
      barcode: '1234567890',
      warehouseQuantity: 50,
      storeQuantity: 20,
    );

    test('يجب أن ينجح في إنشاء منتج صحيح', () async {
      // Arrange
      when(
        mockProductRepository.createProduct(any),
      ).thenAnswer((_) async => Future<void>.value());

      // Act
      await useCase.call(testProduct);

      // Assert
      verify(mockProductRepository.createProduct(testProduct));
      verifyNoMoreInteractions(mockProductRepository);
    });

    test('يجب أن يفشل عند إدخال منتج باسم فارغ', () async {
      // Arrange
      const invalidProduct = Product(
        name: '',
        description: 'وصف المنتج التجريبي',
        category: 'إلكترونيات',
        unit: 'قطعة',
        lastPurchasePrice: 100.0,
        wholesalePrice: 120.0,
        retailPrice: 150.0,
        minStockQuantity: 10,
        barcode: '1234567890',
        warehouseQuantity: 50,
        storeQuantity: 20,
      );

      // Act & Assert
      expect(
        () async => await useCase.call(invalidProduct),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockProductRepository.createProduct(any));
    });

    test('يجب أن يفشل عند إدخال منتج بسعر جملة سالب', () async {
      // Arrange
      const invalidProduct = Product(
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'إلكترونيات',
        unit: 'قطعة',
        lastPurchasePrice: 100.0,
        wholesalePrice: -120.0,
        retailPrice: 150.0,
        minStockQuantity: 10,
        barcode: '1234567890',
        warehouseQuantity: 50,
        storeQuantity: 20,
      );

      // Act & Assert
      expect(
        () async => await useCase.call(invalidProduct),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockProductRepository.createProduct(any));
    });

    test('يجب أن يفشل عند إدخال منتج بسعر تجزئة سالب', () async {
      // Arrange
      const invalidProduct = Product(
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'إلكترونيات',
        unit: 'قطعة',
        lastPurchasePrice: 100.0,
        wholesalePrice: 120.0,
        retailPrice: -150.0,
        minStockQuantity: 10,
        barcode: '1234567890',
        warehouseQuantity: 50,
        storeQuantity: 20,
      );

      // Act & Assert
      expect(
        () async => await useCase.call(invalidProduct),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockProductRepository.createProduct(any));
    });

    test('يجب أن يفشل عند إدخال منتج بكمية مخزن سالبة', () async {
      // Arrange
      const invalidProduct = Product(
        name: 'منتج تجريبي',
        description: 'وصف المنتج التجريبي',
        category: 'إلكترونيات',
        unit: 'قطعة',
        lastPurchasePrice: 100.0,
        wholesalePrice: 120.0,
        retailPrice: 150.0,
        minStockQuantity: 10,
        barcode: '1234567890',
        warehouseQuantity: -50,
        storeQuantity: 20,
      );

      // Act & Assert
      expect(
        () async => await useCase.call(invalidProduct),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockProductRepository.createProduct(any));
    });

    test('يجب أن يرمي استثناء عند فشل المستودع', () async {
      // Arrange
      when(
        mockProductRepository.createProduct(any),
      ).thenThrow(Exception('فشل في قاعدة البيانات'));

      // Act & Assert
      expect(
        () async => await useCase.call(testProduct),
        throwsA(isA<Exception>()),
      );
      verify(mockProductRepository.createProduct(testProduct));
    });
  });
}
