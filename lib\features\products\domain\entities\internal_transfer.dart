import '../../data/models/internal_transfer_model.dart';

class InternalTransfer {
  final int? id;
  final int productId;
  final DateTime transferDate;
  final int quantity;
  final double retailPriceAtTransfer;
  final double costAtTransfer;
  final double totalValue;

  const InternalTransfer({
    this.id,
    required this.productId,
    required this.transferDate,
    required this.quantity,
    required this.retailPriceAtTransfer,
    required this.costAtTransfer,
    required this.totalValue,
  });

  // Create InternalTransfer from InternalTransferModel
  factory InternalTransfer.fromModel(InternalTransferModel model) {
    return InternalTransfer(
      id: model.id,
      productId: model.productId,
      transferDate: model.transferDate,
      quantity: model.quantity,
      retailPriceAtTransfer: model.retailPriceAtTransfer,
      costAtTransfer: model.costAtTransfer,
      totalValue: model.totalValue,
    );
  }

  // Business logic methods
  double get profitMargin => retailPriceAtTransfer - costAtTransfer;
  double get profitPercentage =>
      costAtTransfer > 0 ? (profitMargin / costAtTransfer) * 100 : 0;
  double get totalProfit => profitMargin * quantity;

  // Copy with method for updates
  InternalTransfer copyWith({
    int? id,
    int? productId,
    DateTime? transferDate,
    int? quantity,
    double? retailPriceAtTransfer,
    double? costAtTransfer,
    double? totalValue,
  }) {
    return InternalTransfer(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      transferDate: transferDate ?? this.transferDate,
      quantity: quantity ?? this.quantity,
      retailPriceAtTransfer:
          retailPriceAtTransfer ?? this.retailPriceAtTransfer,
      costAtTransfer: costAtTransfer ?? this.costAtTransfer,
      totalValue: totalValue ?? this.totalValue,
    );
  }

  @override
  String toString() {
    return 'InternalTransfer(id: $id, productId: $productId, '
        'transferDate: $transferDate, quantity: $quantity, '
        'retailPriceAtTransfer: $retailPriceAtTransfer, costAtTransfer: $costAtTransfer, '
        'totalValue: $totalValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InternalTransfer &&
        other.id == id &&
        other.productId == productId &&
        other.transferDate == transferDate &&
        other.quantity == quantity &&
        other.retailPriceAtTransfer == retailPriceAtTransfer &&
        other.costAtTransfer == costAtTransfer &&
        other.totalValue == totalValue;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        transferDate.hashCode ^
        quantity.hashCode ^
        retailPriceAtTransfer.hashCode ^
        costAtTransfer.hashCode ^
        totalValue.hashCode;
  }
}
