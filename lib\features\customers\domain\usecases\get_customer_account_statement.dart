import '../entities/customer_account.dart';
import '../repositories/customer_account_repository.dart';

class GetCustomerAccountStatementUseCase {
  final CustomerAccountRepository _repository;

  GetCustomerAccountStatementUseCase(this._repository);

  Future<List<CustomerAccount>> call(
    int customerId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    if (customerId <= 0) {
      throw Exception('Customer ID must be greater than 0');
    }

    if (fromDate != null && toDate != null && fromDate.isAfter(toDate)) {
      throw Exception('From date cannot be after to date');
    }

    try {
      return await _repository.getCustomerAccountStatement(
        customerId,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get customer account statement: $e');
    }
  }

  Future<double> getCustomerBalance(int customerId) async {
    if (customerId <= 0) {
      throw Exception('Customer ID must be greater than 0');
    }

    try {
      return await _repository.getCustomerBalance(customerId);
    } catch (e) {
      throw Exception('Failed to get customer balance: $e');
    }
  }

  Future<Map<String, dynamic>> getCustomerAccountStatistics(
    int customerId,
  ) async {
    if (customerId <= 0) {
      throw Exception('Customer ID must be greater than 0');
    }

    try {
      return await _repository.getCustomerAccountStatistics(customerId);
    } catch (e) {
      throw Exception('Failed to get customer account statistics: $e');
    }
  }
}
