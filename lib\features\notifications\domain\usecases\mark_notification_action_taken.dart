import '../repositories/notification_repository.dart';

class MarkNotificationActionTakenUseCase {
  final NotificationRepository _repository;

  MarkNotificationActionTakenUseCase(this._repository);

  Future<void> call(int id) async {
    if (id <= 0) {
      throw Exception('Notification ID must be greater than 0');
    }

    try {
      await _repository.markNotificationActionTaken(id);
    } catch (e) {
      throw Exception('Failed to mark notification action as taken: $e');
    }
  }
}
