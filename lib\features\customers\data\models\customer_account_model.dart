class CustomerAccountModel {
  final int? id;
  final int customerId;
  final DateTime transactionDate;
  final String type; // 'sale_invoice', 'retail_debt', 'payment', 'return'
  final double amount;
  final String? description;
  final int? relatedInvoiceId;
  final bool isPaid;

  const CustomerAccountModel({
    this.id,
    required this.customerId,
    required this.transactionDate,
    required this.type,
    required this.amount,
    this.description,
    this.relatedInvoiceId,
    this.isPaid = false,
  });

  // Convert from Map (from database)
  factory CustomerAccountModel.fromMap(Map<String, dynamic> map) {
    return CustomerAccountModel(
      id: map['id'] as int?,
      customerId: map['customerId'] as int,
      transactionDate: DateTime.parse(map['transactionDate'] as String),
      type: map['type'] as String,
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] as String?,
      relatedInvoiceId: map['relatedInvoiceId'] as int?,
      isPaid: (map['isPaid'] as int) == 1,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'transactionDate': transactionDate.toIso8601String(),
      'type': type,
      'amount': amount,
      'description': description,
      'relatedInvoiceId': relatedInvoiceId,
      'isPaid': isPaid ? 1 : 0,
    };
  }

  // Copy with method for updates
  CustomerAccountModel copyWith({
    int? id,
    int? customerId,
    DateTime? transactionDate,
    String? type,
    double? amount,
    String? description,
    int? relatedInvoiceId,
    bool? isPaid,
  }) {
    return CustomerAccountModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      transactionDate: transactionDate ?? this.transactionDate,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
      isPaid: isPaid ?? this.isPaid,
    );
  }

  @override
  String toString() {
    return 'CustomerAccountModel(id: $id, customerId: $customerId, '
        'transactionDate: $transactionDate, type: $type, amount: $amount, '
        'description: $description, relatedInvoiceId: $relatedInvoiceId, '
        'isPaid: $isPaid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerAccountModel &&
        other.id == id &&
        other.customerId == customerId &&
        other.transactionDate == transactionDate &&
        other.type == type &&
        other.amount == amount &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId &&
        other.isPaid == isPaid;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        transactionDate.hashCode ^
        type.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        relatedInvoiceId.hashCode ^
        isPaid.hashCode;
  }
}
