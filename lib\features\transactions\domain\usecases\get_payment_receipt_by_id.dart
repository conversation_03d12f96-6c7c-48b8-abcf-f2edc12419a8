import '../entities/payment_receipt.dart';
import '../repositories/payment_receipt_repository.dart';

/// Use case لجلب سند قبض أو دفع محدد بواسطة ID
class GetPaymentReceiptByIdUseCase {
  final PaymentReceiptRepository _repository;

  GetPaymentReceiptByIdUseCase(this._repository);

  /// جلب سند محدد بواسطة ID
  Future<PaymentReceipt?> call(int id) async {
    try {
      // التحقق من صحة ID
      if (id <= 0) {
        throw Exception('معرف السند غير صحيح');
      }

      return await _repository.getPaymentReceiptById(id);
    } catch (e) {
      throw Exception('فشل في جلب السند: ${e.toString()}');
    }
  }

  /// التحقق من وجود سند بـ ID محدد
  Future<bool> exists(int id) async {
    try {
      final receipt = await call(id);
      return receipt != null;
    } catch (e) {
      return false;
    }
  }
}

/// معاملات لـ GetPaymentReceiptByIdUseCase
class GetPaymentReceiptByIdParams {
  final int id;

  const GetPaymentReceiptByIdParams({required this.id});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GetPaymentReceiptByIdParams && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'GetPaymentReceiptByIdParams(id: $id)';
}
