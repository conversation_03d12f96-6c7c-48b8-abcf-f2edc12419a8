import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

class BackButtonHandler extends StatefulWidget {
  final Widget child;

  const BackButtonHandler({super.key, required this.child});

  @override
  State<BackButtonHandler> createState() => _BackButtonHandlerState();
}

class _BackButtonHandlerState extends State<BackButtonHandler> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        await _handleBackPress(context);
      },
      child: widget.child,
    );
  }

  Future<void> _handleBackPress(BuildContext context) async {
    if (!context.mounted) return;

    final currentRoute = GoRouterState.of(context).fullPath ?? '/';

    // إذا كنا في لوحة التحكم الرئيسية، اعرض تأكيد الخروج
    if (currentRoute == '/') {
      final shouldExit = await _showExitConfirmation(context);
      if (shouldExit && context.mounted) {
        SystemNavigator.pop();
      }
      return;
    }

    // إذا كان بالإمكان العودة (يوجد مسار سابق)، افعل ذلك
    if (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
      return;
    }

    // إذا لم يكن بالإمكان العودة، اذهب للوحة التحكم
    if (context.mounted) {
      context.go('/');
    }
  }

  Future<bool> _showExitConfirmation(BuildContext context) async {
    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الخروج', textAlign: TextAlign.center),
        content: const Text(
          'هل تريد الخروج من التطبيق؟',
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('خروج'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
