// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in market/test/features/sales/domain/usecases/create_sale_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:market/features/sales/domain/entities/sale.dart' as _i4;
import 'package:market/features/sales/domain/entities/sale_item.dart' as _i5;
import 'package:market/features/sales/domain/repositories/sale_repository.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SaleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSaleRepository extends _i1.Mock implements _i2.SaleRepository {
  MockSaleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<int> createSale(_i4.Sale? sale, List<_i5.SaleItem>? items) =>
      (super.noSuchMethod(
            Invocation.method(#createSale, [sale, items]),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  _i3.Future<List<_i4.Sale>> getAllSales() =>
      (super.noSuchMethod(
            Invocation.method(#getAllSales, []),
            returnValue: _i3.Future<List<_i4.Sale>>.value(<_i4.Sale>[]),
          )
          as _i3.Future<List<_i4.Sale>>);

  @override
  _i3.Future<_i4.Sale?> getSaleById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getSaleById, [id]),
            returnValue: _i3.Future<_i4.Sale?>.value(),
          )
          as _i3.Future<_i4.Sale?>);

  @override
  _i3.Future<List<_i5.SaleItem>> getSaleItems(int? saleId) =>
      (super.noSuchMethod(
            Invocation.method(#getSaleItems, [saleId]),
            returnValue: _i3.Future<List<_i5.SaleItem>>.value(<_i5.SaleItem>[]),
          )
          as _i3.Future<List<_i5.SaleItem>>);

  @override
  _i3.Future<void> updateSale(_i4.Sale? sale) =>
      (super.noSuchMethod(
            Invocation.method(#updateSale, [sale]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateSaleWithItems(
    _i4.Sale? sale,
    List<_i5.SaleItem>? items,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateSaleWithItems, [sale, items]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteSale(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteSale, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i4.Sale>> getSalesByCustomer(int? customerId) =>
      (super.noSuchMethod(
            Invocation.method(#getSalesByCustomer, [customerId]),
            returnValue: _i3.Future<List<_i4.Sale>>.value(<_i4.Sale>[]),
          )
          as _i3.Future<List<_i4.Sale>>);

  @override
  _i3.Future<List<_i4.Sale>> getSalesByStatus(String? status) =>
      (super.noSuchMethod(
            Invocation.method(#getSalesByStatus, [status]),
            returnValue: _i3.Future<List<_i4.Sale>>.value(<_i4.Sale>[]),
          )
          as _i3.Future<List<_i4.Sale>>);

  @override
  _i3.Future<List<_i4.Sale>> getSalesByPaymentMethod(String? paymentMethod) =>
      (super.noSuchMethod(
            Invocation.method(#getSalesByPaymentMethod, [paymentMethod]),
            returnValue: _i3.Future<List<_i4.Sale>>.value(<_i4.Sale>[]),
          )
          as _i3.Future<List<_i4.Sale>>);

  @override
  _i3.Future<Map<String, dynamic>> getSalesStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getSalesStatistics, []),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);
}
