import '../entities/expense.dart';

abstract class ExpenseRepository {
  Future<List<Expense>> getAllExpenses();
  Future<Expense?> getExpenseById(int id);
  Future<int> createExpense(Expense expense);
  Future<void> updateExpense(Expense expense);
  Future<void> deleteExpense(int id);
  Future<List<Expense>> getExpensesByCategory(String category);
  Future<List<Expense>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
  Future<double> getTotalExpensesByCategory(String category);
  Future<double> getTotalExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );
}
