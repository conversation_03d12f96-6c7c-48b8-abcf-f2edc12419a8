import '../entities/app_notification.dart';
import '../repositories/notification_repository.dart';

class GetAllNotificationsUseCase {
  final NotificationRepository _repository;

  GetAllNotificationsUseCase(this._repository);

  Future<List<AppNotification>> call({int limit = 50, int offset = 0}) async {
    try {
      return await _repository.getNotifications(limit: limit, offset: offset);
    } catch (e) {
      throw Exception('Failed to get all notifications: $e');
    }
  }
}
