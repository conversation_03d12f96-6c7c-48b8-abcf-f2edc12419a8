import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/repositories/product_repository.dart';

class GetProductByIdUseCase {
  final ProductRepository _repository;

  GetProductByIdUseCase(this._repository);

  Future<Product?> call(int productId) async {
    if (productId <= 0) {
      throw Exception('Invalid product ID');
    }

    return await _repository.getProductById(productId);
  }
}
