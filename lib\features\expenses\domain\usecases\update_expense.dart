import '../entities/expense.dart';
import '../repositories/expense_repository.dart';

class UpdateExpense {
  final ExpenseRepository _repository;

  UpdateExpense(this._repository);

  Future<void> call(Expense expense) async {
    // Validation
    if (expense.id == null) {
      throw Exception('Expense ID cannot be null for update');
    }

    if (expense.description.trim().isEmpty) {
      throw Exception('Expense description cannot be empty');
    }

    if (expense.amount <= 0) {
      throw Exception('Expense amount must be greater than zero');
    }

    if (expense.category.trim().isEmpty) {
      throw Exception('Expense category cannot be empty');
    }

    // Validate expense date is not in the future
    if (expense.expenseDate.isAfter(DateTime.now())) {
      throw Exception('Expense date cannot be in the future');
    }

    try {
      // Check if expense exists
      final existingExpense = await _repository.getExpenseById(expense.id!);
      if (existingExpense == null) {
        throw Exception('Expense not found');
      }

      await _repository.updateExpense(expense);
    } catch (e) {
      throw Exception('Failed to update expense: $e');
    }
  }
}
