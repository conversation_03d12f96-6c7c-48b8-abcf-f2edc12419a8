class SaleModel {
  final int? id;
  final int? customerId;
  final DateTime saleDate;
  final double totalAmount;
  final double discountAmount;
  final double totalPaidAmount;
  final double costOfGoodsSold; // جديد: تكلفة البضاعة المباعة
  final double profit; // جديد: الربح الإجمالي
  final String paymentMethod;
  final String? notes;
  final String status;

  const SaleModel({
    this.id,
    this.customerId,
    required this.saleDate,
    required this.totalAmount,
    this.discountAmount = 0.0,
    this.totalPaidAmount = 0.0,
    this.costOfGoodsSold = 0.0, // قيمة افتراضية
    this.profit = 0.0, // قيمة افتراضية
    required this.paymentMethod,
    this.notes,
    required this.status,
  });

  // Convert from Map (from database)
  factory SaleModel.fromMap(Map<String, dynamic> map) {
    return SaleModel(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      saleDate: DateTime.parse(map['saleDate'] as String),
      totalAmount: (map['totalAmount'] as num).toDouble(),
      discountAmount: (map['discountAmount'] as num?)?.toDouble() ?? 0.0,
      totalPaidAmount: (map['totalPaidAmount'] as num?)?.toDouble() ?? 0.0,
      costOfGoodsSold:
          (map['cost_of_goods_sold'] as num?)?.toDouble() ?? 0.0, // جديد
      profit: (map['profit'] as num?)?.toDouble() ?? 0.0, // جديد
      paymentMethod: map['paymentMethod'] as String,
      notes: map['notes'] as String?,
      status: map['status'] as String,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'saleDate': saleDate.toIso8601String(),
      'totalAmount': totalAmount,
      'discountAmount': discountAmount,
      'totalPaidAmount': totalPaidAmount,
      'cost_of_goods_sold': costOfGoodsSold, // جديد
      'profit': profit, // جديد
      'paymentMethod': paymentMethod,
      'notes': notes,
      'status': status,
    };
  }

  // Copy with method for updates
  SaleModel copyWith({
    int? id,
    int? customerId,
    DateTime? saleDate,
    double? totalAmount,
    double? discountAmount,
    double? totalPaidAmount,
    double? costOfGoodsSold, // جديد
    double? profit, // جديد
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return SaleModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      saleDate: saleDate ?? this.saleDate,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalPaidAmount: totalPaidAmount ?? this.totalPaidAmount,
      costOfGoodsSold: costOfGoodsSold ?? this.costOfGoodsSold, // جديد
      profit: profit ?? this.profit, // جديد
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'SaleModel(id: $id, customerId: $customerId, saleDate: $saleDate, '
        'totalAmount: $totalAmount, totalPaidAmount: $totalPaidAmount, '
        'costOfGoodsSold: $costOfGoodsSold, profit: $profit, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SaleModel &&
        other.id == id &&
        other.customerId == customerId &&
        other.saleDate == saleDate &&
        other.totalAmount == totalAmount &&
        other.discountAmount == discountAmount &&
        other.totalPaidAmount == totalPaidAmount &&
        other.costOfGoodsSold == costOfGoodsSold &&
        other.profit == profit &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        saleDate.hashCode ^
        totalAmount.hashCode ^
        discountAmount.hashCode ^
        totalPaidAmount.hashCode ^
        costOfGoodsSold.hashCode ^
        profit.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
