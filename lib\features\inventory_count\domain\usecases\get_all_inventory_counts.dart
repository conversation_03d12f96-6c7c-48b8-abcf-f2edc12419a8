import '../entities/inventory_count.dart';
import '../repositories/inventory_count_repository.dart';

class GetAllInventoryCountsUseCase {
  final InventoryCountRepository _repository;

  GetAllInventoryCountsUseCase(this._repository);

  Future<List<InventoryCount>> call() async {
    try {
      return await _repository.getAllInventoryCounts();
    } catch (e) {
      throw Exception('Failed to get all inventory counts: $e');
    }
  }
}
