import 'package:flutter/material.dart';
import '../../domain/entities/payment_receipt.dart';
import '../../domain/usecases/create_payment_receipt.dart';
import '../../domain/usecases/get_all_payment_receipts.dart';
import '../../domain/usecases/get_payment_receipt_by_id.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../customers/domain/entities/customer_account.dart';
import '../../../suppliers/presentation/providers/supplier_provider.dart';
import '../../../suppliers/domain/entities/supplier_account.dart';

/// Provider لإدارة حالة سندات القبض والدفع
class PaymentReceiptProvider extends ChangeNotifier {
  final CreatePaymentReceiptUseCase _createPaymentReceiptUseCase;
  final GetAllPaymentReceiptsUseCase _getAllPaymentReceiptsUseCase;
  final GetPaymentReceiptByIdUseCase _getPaymentReceiptByIdUseCase;
  final CustomerProvider _customerProvider;
  // +++ تأكد من إضافة supplierProvider إلى المُنشئ +++
  final SupplierProvider _supplierProvider;

  PaymentReceiptProvider({
    required CreatePaymentReceiptUseCase createPaymentReceiptUseCase,
    required GetAllPaymentReceiptsUseCase getAllPaymentReceiptsUseCase,
    required GetPaymentReceiptByIdUseCase getPaymentReceiptByIdUseCase,
    required CustomerProvider customerProvider,
    required SupplierProvider supplierProvider,
  }) : _createPaymentReceiptUseCase = createPaymentReceiptUseCase,
       _getAllPaymentReceiptsUseCase = getAllPaymentReceiptsUseCase,
       _getPaymentReceiptByIdUseCase = getPaymentReceiptByIdUseCase,
       _customerProvider = customerProvider,
       _supplierProvider = supplierProvider;

  // حالة البيانات
  List<PaymentReceipt> _paymentReceipts = [];
  PaymentReceipt? _selectedPaymentReceipt;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<PaymentReceipt> get paymentReceipts => _paymentReceipts;
  PaymentReceipt? get selectedPaymentReceipt => _selectedPaymentReceipt;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Getters للفلترة
  List<PaymentReceipt> get paymentInReceipts => _paymentReceipts
      .where((receipt) => receipt.type == 'payment_in')
      .toList();

  List<PaymentReceipt> get paymentOutReceipts => _paymentReceipts
      .where((receipt) => receipt.type == 'payment_out')
      .toList();

  List<PaymentReceipt> get customerReceipts => _paymentReceipts
      .where((receipt) => receipt.relatedEntityType == 'customer')
      .toList();

  List<PaymentReceipt> get supplierReceipts => _paymentReceipts
      .where((receipt) => receipt.relatedEntityType == 'supplier')
      .toList();

  /// جلب جميع السندات
  Future<void> fetchPaymentReceipts() async {
    _setLoading(true);
    _clearError();

    try {
      _paymentReceipts = await _getAllPaymentReceiptsUseCase();
      notifyListeners();
    } catch (e) {
      _setError('فشل في جلب السندات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// جلب سند محدد بواسطة ID
  Future<void> getPaymentReceiptById(int id) async {
    // تأجيل التحديث لتجنب مشكلة setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setLoading(true);
      _clearError();
    });

    try {
      _selectedPaymentReceipt = await _getPaymentReceiptByIdUseCase(id);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setError('فشل في جلب السند: ${e.toString()}');
      });
    } finally {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setLoading(false);
      });
    }
  }

  /// إنشاء سند جديد مع إغلاق الشاشة عند النجاح
  Future<bool> createPaymentReceiptWithNavigation(
    BuildContext context, {
    int? relatedEntityId,
    required String relatedEntityType,
    required DateTime transactionDate,
    required double amount,
    required String type,
    required String paymentMethod,
    String? description,
    int? relatedInvoiceId,
  }) async {
    final success = await createPaymentReceipt(
      relatedEntityId: relatedEntityId,
      relatedEntityType: relatedEntityType,
      transactionDate: transactionDate,
      amount: amount,
      type: type,
      paymentMethod: paymentMethod,
      description: description,
      relatedInvoiceId: relatedInvoiceId,
    );

    if (success && context.mounted) {
      Navigator.of(context).pop(true);
    }

    return success;
  }

  /// إنشاء سند جديد
  Future<bool> createPaymentReceipt({
    int? relatedEntityId,
    required String relatedEntityType,
    required DateTime transactionDate,
    required double amount,
    required String type,
    required String paymentMethod,
    String? description,
    int? relatedInvoiceId,
  }) async {
    print(
      '🚀🚀🚀 DEBUG: بداية دالة createPaymentReceipt - type: $type, relatedEntityType: $relatedEntityType, relatedEntityId: $relatedEntityId',
    );
    _setLoading(true);
    _clearError();

    try {
      final params = CreatePaymentReceiptParams(
        relatedEntityId: relatedEntityId,
        relatedEntityType: relatedEntityType,
        transactionDate: transactionDate,
        amount: amount,
        type: type,
        paymentMethod: paymentMethod,
        description: description,
        relatedInvoiceId: relatedInvoiceId,
      );

      final newReceiptId = await _createPaymentReceiptUseCase(params);

      // +++ الإصلاح الجذري يبدأ هنا +++
      print('🚀 DEBUG: بداية معالجة إضافة قيود الحسابات بعد إنشاء السند');
      // التحقق إذا كان السند هو "سند قبض" لـ "عميل"
      print(
        '🔍 DEBUG: تحقق من الشرط - relatedEntityId: $relatedEntityId, relatedEntityType: $relatedEntityType, type: $type',
      );
      print('🔍 DEBUG: تفاصيل الشرط:');
      print('  - relatedEntityId != null: ${relatedEntityId != null}');
      print(
        '  - relatedEntityType == "customer": ${relatedEntityType == 'customer'}',
      );
      print('  - type == "payment_in": ${type == 'payment_in'}');

      if (relatedEntityId != null &&
          relatedEntityType == 'customer' &&
          type == 'payment_in') {
        print('✅ DEBUG: الشرط تحقق - سيتم إضافة قيد حساب العميل');
        print(
          '🔍 DEBUG: جاري إعداد إضافة قيد حساب العميل من سند القبض. EntityId: $relatedEntityId, Amount: $amount, InvoiceId: $relatedInvoiceId',
        );
        print('🔍 DEBUG: السطر 166 - بعد الرسالة الأولى مباشرة');

        // 1. إنشاء القيد المحاسبي الذي سيتم إضافته لحساب العميل
        print('🔍 DEBUG: السطر 169 - قبل إنشاء كائن CustomerAccount...');
        print('🔍 DEBUG: إنشاء كائن CustomerAccount...');
        final accountEntry = CustomerAccount(
          customerId: relatedEntityId,
          transactionDate: transactionDate,
          type: 'payment_in', // نوع مخصص للدفعات لتمييزه عن الفواتير
          amount: amount, // المبلغ الذي تم قبضه
          description: description ?? 'سند قبض رقم #$newReceiptId',
          relatedInvoiceId: relatedInvoiceId,
          isPaid: true, // السندات دائماً تُعتبر مدفوعة
        );
        print('✅ DEBUG: تم إنشاء كائن CustomerAccount بنجاح.');

        // 2. استدعاء CustomerProvider مباشرةً لإضافة القيد وتحديث الواجهات
        print(
          '🔍 DEBUG: محاولة استدعاء _customerProvider.addCustomerAccountEntry...',
        );
        await _customerProvider.addCustomerAccountEntry(accountEntry);
        print('✅ DEBUG: تم استدعاء addCustomerAccountEntry لسند القبض بنجاح.');
      } else if (relatedEntityId != null &&
          relatedEntityType == 'supplier' &&
          type == 'payment_out') {
        print(
          '🔍 DEBUG: Calling addSupplierAccountEntry for payment_out. SupplierID: $relatedEntityId, Amount: $amount',
        );

        // **المنطق الجديد للموردين**
        final accountEntry = SupplierAccount(
          supplierId: relatedEntityId,
          transactionDate: transactionDate,
          type: 'payment_out', // دفعة لمورد
          amount: amount,
          description: description ?? 'سند دفع رقم #$newReceiptId',
          relatedInvoiceId: relatedInvoiceId,
        );

        print(
          '🔍 DEBUG: Payment account entry details: supplierId=$relatedEntityId, amount=$amount, type=payment_out',
        );

        // استدعاء مزود الموردين لتسجيل الحركة - بدون تجاهل الأخطاء
        await _supplierProvider.addSupplierAccountEntry(accountEntry);

        print(
          '✅ DEBUG: addSupplierAccountEntry call completed for payment_out.',
        );
      }
      // --- نهاية التعديل ---

      // تحديث قائمة السندات نفسها
      await fetchPaymentReceipts();

      return true;
    } catch (e) {
      // تحسين رسائل الخطأ باللغة العربية
      String errorMessage = 'فشل في إنشاء السند';
      if (e.toString().contains('Customer')) {
        errorMessage = 'خطأ في بيانات العميل';
      } else if (e.toString().contains('Supplier')) {
        errorMessage = 'خطأ في بيانات المورد';
      } else if (e.toString().contains('Amount')) {
        errorMessage = 'خطأ في المبلغ المدخل';
      } else if (e.toString().contains('Payment method')) {
        errorMessage = 'خطأ في طريقة الدفع';
      } else if (e.toString().contains('Database')) {
        errorMessage = 'خطأ في قاعدة البيانات';
      }
      _setError('$errorMessage: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// جلب السندات حسب الكيان (عميل أو مورد)
  Future<List<PaymentReceipt>> getPaymentReceiptsByEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      return await _getAllPaymentReceiptsUseCase.getByEntity(
        entityId: entityId,
        entityType: entityType,
      );
    } catch (e) {
      _setError('فشل في جلب السندات للكيان: ${e.toString()}');
      return [];
    }
  }

  /// جلب السندات حسب نوع المعاملة
  Future<List<PaymentReceipt>> getPaymentReceiptsByType(String type) async {
    try {
      return await _getAllPaymentReceiptsUseCase.getByTransactionType(type);
    } catch (e) {
      _setError('فشل في جلب السندات حسب النوع: ${e.toString()}');
      return [];
    }
  }

  /// جلب السندات في فترة زمنية
  Future<List<PaymentReceipt>> getPaymentReceiptsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      return await _getAllPaymentReceiptsUseCase.getByDateRange(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      _setError('فشل في جلب السندات في الفترة المحددة: ${e.toString()}');
      return [];
    }
  }

  /// حساب إجمالي المبالغ المقبوضة
  double get totalPaymentsIn {
    return paymentInReceipts.fold(0.0, (sum, receipt) => sum + receipt.amount);
  }

  /// حساب إجمالي المبالغ المدفوعة
  double get totalPaymentsOut {
    return paymentOutReceipts.fold(0.0, (sum, receipt) => sum + receipt.amount);
  }

  /// حساب صافي التدفق النقدي
  double get netCashFlow {
    return totalPaymentsIn - totalPaymentsOut;
  }

  /// تصفية السندات حسب طريقة الدفع
  List<PaymentReceipt> getReceiptsByPaymentMethod(String paymentMethod) {
    return _paymentReceipts
        .where((receipt) => receipt.paymentMethod == paymentMethod)
        .toList();
  }

  /// تصفية السندات حسب التاريخ (اليوم، هذا الأسبوع، هذا الشهر)
  List<PaymentReceipt> getReceiptsByPeriod(String period) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (period) {
      case 'today':
        return _paymentReceipts.where((receipt) {
          final receiptDate = DateTime(
            receipt.transactionDate.year,
            receipt.transactionDate.month,
            receipt.transactionDate.day,
          );
          return receiptDate.isAtSameMomentAs(today);
        }).toList();

      case 'week':
        final weekStart = today.subtract(Duration(days: now.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 6));
        return _paymentReceipts.where((receipt) {
          final receiptDate = receipt.transactionDate;
          return receiptDate.isAfter(
                weekStart.subtract(const Duration(days: 1)),
              ) &&
              receiptDate.isBefore(weekEnd.add(const Duration(days: 1)));
        }).toList();

      case 'month':
        final monthStart = DateTime(now.year, now.month, 1);
        final monthEnd = DateTime(now.year, now.month + 1, 0);
        return _paymentReceipts.where((receipt) {
          final receiptDate = receipt.transactionDate;
          return receiptDate.isAfter(
                monthStart.subtract(const Duration(days: 1)),
              ) &&
              receiptDate.isBefore(monthEnd.add(const Duration(days: 1)));
        }).toList();

      default:
        return _paymentReceipts;
    }
  }

  /// تنظيف البيانات المحددة
  void clearSelectedPaymentReceipt() {
    _selectedPaymentReceipt = null;
    notifyListeners();
  }

  /// تنظيف جميع البيانات
  void clearAll() {
    _paymentReceipts.clear();
    _selectedPaymentReceipt = null;
    _clearError();
    notifyListeners();
  }

  // دوال مساعدة خاصة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
