import '../entities/payment_receipt.dart';

abstract class PaymentReceiptRepository {
  /// Create a new payment receipt
  Future<int> createPaymentReceipt(PaymentReceipt paymentReceipt);

  /// Get payment receipt by ID
  Future<PaymentReceipt?> getPaymentReceiptById(int id);

  /// Get all payment receipts for a specific entity (customer or supplier)
  Future<List<PaymentReceipt>> getPaymentReceiptsByEntity({
    required int entityId,
    required String entityType,
  });

  /// Get all payment receipts for a specific invoice
  Future<List<PaymentReceipt>> getPaymentReceiptsByInvoice(int invoiceId);

  /// Get all payment receipts
  Future<List<PaymentReceipt>> getAllPaymentReceipts();

  /// Update payment receipt
  Future<void> updatePaymentReceipt(PaymentReceipt paymentReceipt);

  /// Delete payment receipt
  Future<void> deletePaymentReceipt(int id);

  /// Get payment receipts by date range
  Future<List<PaymentReceipt>> getPaymentReceiptsByDateRange({
    required DateTime fromDate,
    required DateTime toDate,
    String? entityType,
    int? entityId,
  });

  /// Get total payments for entity
  Future<double> getTotalPaymentsForEntity({
    required int entityId,
    required String entityType,
  });

  /// Get total payments for invoice
  Future<double> getTotalPaymentsForInvoice(int invoiceId);
}
