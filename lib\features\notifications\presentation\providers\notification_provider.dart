import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/app_notification.dart';
import '../../domain/usecases/create_notification.dart';
import '../../domain/usecases/get_all_notifications.dart';
import '../../domain/usecases/mark_notification_as_read.dart';
import '../../domain/usecases/mark_notification_action_taken.dart';
import '../../domain/usecases/get_unread_notifications_count.dart';

class NotificationProvider extends ChangeNotifier {
  final CreateNotificationUseCase _createNotificationUseCase;
  final GetAllNotificationsUseCase _getAllNotificationsUseCase;
  final MarkNotificationAsReadUseCase _markNotificationAsReadUseCase;
  final MarkNotificationActionTakenUseCase _markNotificationActionTakenUseCase;
  final GetUnreadNotificationsCountUseCase _getUnreadNotificationsCountUseCase;

  NotificationProvider()
    : _createNotificationUseCase = GetIt.instance<CreateNotificationUseCase>(),
      _getAllNotificationsUseCase =
          GetIt.instance<GetAllNotificationsUseCase>(),
      _markNotificationAsReadUseCase =
          GetIt.instance<MarkNotificationAsReadUseCase>(),
      _markNotificationActionTakenUseCase =
          GetIt.instance<MarkNotificationActionTakenUseCase>(),
      _getUnreadNotificationsCountUseCase =
          GetIt.instance<GetUnreadNotificationsCountUseCase>();

  // State variables
  final List<AppNotification> _notifications = [];
  int _unreadCount = 0;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<AppNotification> get notifications => _notifications;
  int get unreadCount => _unreadCount;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Fetch all notifications
  Future<void> fetchNotifications({int limit = 50, int offset = 0}) async {
    _setLoading(true);
    _clearError();

    try {
      if (offset == 0) {
        _notifications.clear();
      }

      final newNotifications = await _getAllNotificationsUseCase.call(
        limit: limit,
        offset: offset,
      );

      _notifications.addAll(newNotifications);
      await _updateUnreadCount();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load notifications: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create notification
  Future<bool> createNotification(AppNotification notification) async {
    try {
      await _createNotificationUseCase.call(notification);
      await fetchNotifications(); // Refresh list
      return true;
    } catch (e) {
      _setError('فشل في إنشاء التنبيه: ${e.toString()}');
      return false;
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(int id) async {
    try {
      await _markNotificationAsReadUseCase.call(id);

      // Update local state
      final index = _notifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        await _updateUnreadCount();
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('فشل في تحديد التنبيه كمقروء: ${e.toString()}');
      return false;
    }
  }

  // Mark notification action as taken
  Future<bool> markActionTaken(int id) async {
    try {
      await _markNotificationActionTakenUseCase.call(id);

      // Update local state
      final index = _notifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(
          isActionTaken: true,
          isRead: true,
        );
        await _updateUnreadCount();
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('فشل في تحديد إجراء التنبيه كمنفذ: ${e.toString()}');
      return false;
    }
  }

  // Update unread count
  Future<void> _updateUnreadCount() async {
    try {
      _unreadCount = await _getUnreadNotificationsCountUseCase.call();
    } catch (e) {
      // Don't show error for count update
    }
  }

  // Get unread notifications
  List<AppNotification> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Get notifications by type
  List<AppNotification> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear notifications list
  void clearNotifications() {
    _notifications.clear();
    _unreadCount = 0;
    notifyListeners();
  }

  // Initialize - fetch notifications and unread count
  Future<void> initialize() async {
    await fetchNotifications();
  }
}
