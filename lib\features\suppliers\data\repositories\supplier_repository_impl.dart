import '../../domain/entities/supplier.dart';
import '../../domain/repositories/supplier_repository.dart';
import '../datasources/supplier_database_service.dart';
import '../models/supplier_model.dart';

class SupplierRepositoryImpl implements SupplierRepository {
  final SupplierDatabaseService _databaseService;

  SupplierRepositoryImpl(this._databaseService);

  @override
  Future<List<Supplier>> getAllSuppliers() async {
    try {
      final supplierModels = await _databaseService.getAllSuppliers();
      return supplierModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to get suppliers: $e');
    }
  }

  @override
  Future<Supplier?> getSupplierById(int id) async {
    try {
      final supplierModel = await _databaseService.getSupplierById(id);
      if (supplierModel != null) {
        return _modelToEntity(supplierModel);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get supplier by id: $e');
    }
  }

  @override
  Future<int> createSupplier(Supplier supplier) async {
    try {
      final supplierModel = _entityToModel(supplier);
      return await _databaseService.createSupplier(supplierModel);
    } catch (e) {
      throw Exception('Failed to create supplier: $e');
    }
  }

  @override
  Future<void> updateSupplier(Supplier supplier) async {
    try {
      final supplierModel = _entityToModel(supplier);
      await _databaseService.updateSupplier(supplierModel);
    } catch (e) {
      throw Exception('Failed to update supplier: $e');
    }
  }

  @override
  Future<void> deleteSupplier(int id) async {
    try {
      await _databaseService.deleteSupplier(id);
    } catch (e) {
      throw Exception('Failed to delete supplier: $e');
    }
  }

  @override
  Future<List<Supplier>> searchSuppliers(String query) async {
    try {
      final supplierModels = await _databaseService.searchSuppliers(query);
      return supplierModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to search suppliers: $e');
    }
  }

  Supplier _modelToEntity(SupplierModel model) {
    return Supplier(
      id: model.id,
      name: model.name,
      phone: model.phone,
      email: model.email,
      address: model.address,
      contactPerson: model.contactPerson,
      notes: model.notes,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  SupplierModel _entityToModel(Supplier entity) {
    return SupplierModel(
      id: entity.id,
      name: entity.name,
      phone: entity.phone,
      email: entity.email,
      address: entity.address,
      contactPerson: entity.contactPerson,
      notes: entity.notes,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
