import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/shared_widgets/custom_app_bar.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/presentation/providers/category_provider.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isMultiSelectMode = false;
  final Set<int> _selectedProductIds = <int>{};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductProvider>().fetchProducts();
      context.read<CategoryProvider>().fetchCategories();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = !_isMultiSelectMode;
      if (!_isMultiSelectMode) {
        _selectedProductIds.clear();
      }
    });
  }

  void _toggleProductSelection(int productId) {
    setState(() {
      if (_selectedProductIds.contains(productId)) {
        _selectedProductIds.remove(productId);
      } else {
        _selectedProductIds.add(productId);
      }
    });
  }

  void _selectProduct(int productId) {
    setState(() {
      _isMultiSelectMode = true;
      _selectedProductIds.add(productId);
    });
  }

  Future<void> _deleteSelectedProducts() async {
    if (_selectedProductIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف ${_selectedProductIds.length} منتج؟ هذا الإجراء لا يمكن التراجع عنه!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    if (!mounted) return;
    final productProvider = context.read<ProductProvider>();
    int deletedCount = 0;

    for (final productId in _selectedProductIds) {
      final success = await productProvider.deleteProduct(productId);
      if (success) deletedCount++;
    }

    if (!mounted) return;
    setState(() {
      _isMultiSelectMode = false;
      _selectedProductIds.clear();
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حذف $deletedCount منتج بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _shareSelectedProducts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم مشاركة المنتجات المحددة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: _isMultiSelectMode
          ? '${_selectedProductIds.length} محدد'
          : 'المنتجات',
      customAppBar: CustomAppBar(
        title: _isMultiSelectMode
            ? '${_selectedProductIds.length} محدد'
            : 'المنتجات',
        showBackButton: false,
        leading: _isMultiSelectMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: _toggleMultiSelectMode,
                tooltip: 'إلغاء التحديد',
              )
            : null,
        actions: _isMultiSelectMode
            ? [
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _selectedProductIds.isNotEmpty
                      ? _deleteSelectedProducts
                      : null,
                  tooltip: 'حذف المحدد',
                ),
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _selectedProductIds.isNotEmpty
                      ? _shareSelectedProducts
                      : null,
                  tooltip: 'مشاركة المحدد',
                ),
              ]
            : [
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () => _showFilterDialog(context),
                  tooltip: 'فلترة',
                ),
              ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/products/add'),
        child: const Icon(Icons.add),
      ),
      child: Consumer<ProductProvider>(
        builder: (context, productProvider, child) {
          if (productProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (productProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المنتجات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    productProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => productProvider.fetchProducts(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (productProvider.products.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد منتجات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإضافة منتجات جديدة',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => context.go('/products/add'),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة منتج'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في المنتجات...',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              // Products list
              Expanded(
                child: Builder(
                  builder: (context) {
                    final filteredProducts = productProvider
                        .getFilteredProducts(_searchQuery);

                    if (filteredProducts.isEmpty && _searchQuery.isNotEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج للبحث',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'جرب البحث بكلمات مختلفة',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      itemCount: filteredProducts.length,
                      itemBuilder: (context, index) {
                        final product = filteredProducts[index];
                        final isSelected = _selectedProductIds.contains(
                          product.id,
                        );

                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          child: ListTile(
                            leading: _isMultiSelectMode
                                ? Checkbox(
                                    value: isSelected,
                                    onChanged: (value) {
                                      _toggleProductSelection(product.id!);
                                    },
                                  )
                                : CircleAvatar(
                                    backgroundColor: product.isLowStock
                                        ? Colors.red[100]
                                        : Colors.green[100],
                                    child: Icon(
                                      Icons.inventory_2,
                                      color: product.isLowStock
                                          ? Colors.red[700]
                                          : Colors.green[700],
                                    ),
                                  ),
                            title: Text(product.name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('الفئة: ${product.category}'),
                                Text(
                                  'الكمية: ${product.totalQuantity} ${product.unit}',
                                ),
                                Text(
                                  'جملة: ${product.wholesalePrice.toStringAsFixed(2)} ر.ي | تجزئة: ${product.retailPrice.toStringAsFixed(2)} ر.ي',
                                ),
                              ],
                            ),
                            trailing: _isMultiSelectMode
                                ? null
                                : PopupMenuButton(
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'select',
                                        child: ListTile(
                                          leading: Icon(Icons.check_circle),
                                          title: Text('تحديد'),
                                        ),
                                      ),
                                      const PopupMenuItem(
                                        value: 'view',
                                        child: ListTile(
                                          leading: Icon(Icons.visibility),
                                          title: Text('عرض'),
                                        ),
                                      ),
                                      const PopupMenuItem(
                                        value: 'edit',
                                        child: ListTile(
                                          leading: Icon(Icons.edit),
                                          title: Text('تعديل'),
                                        ),
                                      ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: ListTile(
                                          leading: Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          title: Text(
                                            'حذف',
                                            style: TextStyle(color: Colors.red),
                                          ),
                                        ),
                                      ),
                                    ],
                                    onSelected: (value) {
                                      switch (value) {
                                        case 'select':
                                          _selectProduct(product.id!);
                                          break;
                                        case 'view':
                                          context.go('/products/${product.id}');
                                          break;
                                        case 'edit':
                                          context.go(
                                            '/products/edit/${product.id}',
                                          );
                                          break;
                                        case 'delete':
                                          _showDeleteDialog(
                                            context,
                                            product.id!,
                                            product.name,
                                          );
                                          break;
                                      }
                                    },
                                  ),
                            onTap: _isMultiSelectMode
                                ? () => _toggleProductSelection(product.id!)
                                : () => context.go('/products/${product.id}'),
                            onLongPress: _isMultiSelectMode
                                ? null
                                : () => _selectProduct(product.id!),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final productProvider = context.read<ProductProvider>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة المنتجات'),
        content: Consumer<CategoryProvider>(
          builder: (context, catProvider, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر الفئة:'),
                const SizedBox(height: 16),
                RadioListTile<String?>(
                  title: const Text('جميع الفئات'),
                  value: null,
                  groupValue: productProvider.selectedCategory,
                  onChanged: (value) {
                    productProvider.filterProductsByCategory(null);
                    Navigator.of(context).pop();
                  },
                ),
                ...catProvider.categories.map((category) {
                  return RadioListTile<String?>(
                    title: Text(category.name),
                    value: category.name,
                    groupValue: productProvider.selectedCategory,
                    onChanged: (value) {
                      productProvider.filterProductsByCategory(value);
                      Navigator.of(context).pop();
                    },
                  );
                }),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    int productId,
    String productName,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف المنتج "$productName"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);
              final productProvider = context.read<ProductProvider>();

              navigator.pop();
              final success = await productProvider.deleteProduct(productId);
              if (success && mounted) {
                messenger.showSnackBar(
                  const SnackBar(content: Text('تم حذف المنتج بنجاح')),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
