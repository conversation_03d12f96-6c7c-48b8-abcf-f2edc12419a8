import '../../data/models/purchase_item_model.dart';

class PurchaseItem {
  final int? id;
  final int purchaseId;
  final int productId;
  final int quantity;
  final double unitPrice;
  final String? unit; // الوحدة (كيلو، قطعة، متر، إلخ)

  const PurchaseItem({
    this.id,
    required this.purchaseId,
    required this.productId,
    required this.quantity,
    required this.unitPrice,
    this.unit,
  });

  // Create PurchaseItem from PurchaseItemModel
  factory PurchaseItem.fromModel(PurchaseItemModel model) {
    return PurchaseItem(
      id: model.id,
      purchaseId: model.purchaseId,
      productId: model.productId,
      quantity: model.quantity,
      unitPrice: model.unitPrice,
      unit: model.unit,
    );
  }

  // Business logic methods
  double get totalPrice => quantity * unitPrice;

  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(2)} ج.م';
  String get formattedUnitPrice => '${unitPrice.toStringAsFixed(2)} ج.م';

  // Copy with method for updates
  PurchaseItem copyWith({
    int? id,
    int? purchaseId,
    int? productId,
    int? quantity,
    double? unitPrice,
    String? unit,
  }) {
    return PurchaseItem(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      unit: unit ?? this.unit,
    );
  }

  @override
  String toString() {
    return 'PurchaseItem(id: $id, purchaseId: $purchaseId, productId: $productId, '
        'quantity: $quantity, unitPrice: $unitPrice, unit: $unit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseItem &&
        other.id == id &&
        other.purchaseId == purchaseId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.unit == unit;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        purchaseId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode ^
        unit.hashCode;
  }
}
