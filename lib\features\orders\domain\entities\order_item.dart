import '../../data/models/order_item_model.dart';

class OrderItem {
  final int? id;
  final int orderId;
  final int productId;
  final int quantity;
  final double estimatedUnitPrice;

  const OrderItem({
    this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.estimatedUnitPrice,
  });

  // Create OrderItem from OrderItemModel
  factory OrderItem.fromModel(OrderItemModel model) {
    return OrderItem(
      id: model.id,
      orderId: model.orderId,
      productId: model.productId,
      quantity: model.quantity,
      estimatedUnitPrice: model.estimatedUnitPrice,
    );
  }

  // Business logic methods
  double get totalEstimatedCost => quantity * estimatedUnitPrice;

  // Copy with method for updates
  OrderItem copyWith({
    int? id,
    int? orderId,
    int? productId,
    int? quantity,
    double? estimatedUnitPrice,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      estimatedUnitPrice: estimatedUnitPrice ?? this.estimatedUnitPrice,
    );
  }

  @override
  String toString() {
    return 'OrderItem(id: $id, orderId: $orderId, productId: $productId, '
        'quantity: $quantity, estimatedUnitPrice: $estimatedUnitPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem &&
        other.id == id &&
        other.orderId == orderId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.estimatedUnitPrice == estimatedUnitPrice;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        estimatedUnitPrice.hashCode;
  }
}
