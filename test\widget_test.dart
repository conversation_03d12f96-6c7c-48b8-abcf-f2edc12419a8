// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import 'package:market/my_app.dart';
import 'package:market/core/di/di_container.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';

void main() {
  testWidgets('App loads without crashing', (WidgetTester tester) async {
    // Setup dependency injection for testing
    await setupDependencyInjection();

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<ProductProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<CustomerProvider>(),
          ),
        ],
        child: const MyApp(),
      ),
    );

    // Verify that the app loads with the expected text
    expect(find.text('الواجهة الرئيسية'), findsWidgets);
  });
}
