import '../entities/purchase_batch.dart';
import '../repositories/purchase_batch_repository.dart';

class AddPurchaseBatchUseCase {
  final PurchaseBatchRepository _repository;

  AddPurchaseBatchUseCase(this._repository);

  Future<int> call(PurchaseBatch batch) async {
    // Validation
    if (batch.productId <= 0) {
      throw Exception('Product ID must be greater than 0');
    }

    if (batch.quantity <= 0) {
      throw Exception('Quantity must be greater than 0');
    }

    if (batch.unitPurchasePrice < 0) {
      throw Exception('Unit purchase price cannot be negative');
    }

    if (batch.remainingQuantity < 0) {
      throw Exception('Remaining quantity cannot be negative');
    }

    if (batch.remainingQuantity > batch.quantity) {
      throw Exception(
        'Remaining quantity cannot be greater than total quantity',
      );
    }

    try {
      return await _repository.addBatch(batch);
    } catch (e) {
      throw Exception('Failed to add purchase batch: $e');
    }
  }
}
