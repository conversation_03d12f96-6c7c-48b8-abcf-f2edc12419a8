// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in market/test/features/products/domain/usecases/create_product_use_case_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:market/features/products/domain/entities/product.dart' as _i4;
import 'package:market/features/products/domain/repositories/product_repository.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ProductRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockProductRepository extends _i1.Mock implements _i2.ProductRepository {
  MockProductRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.Product>> getProducts() =>
      (super.noSuchMethod(
            Invocation.method(#getProducts, []),
            returnValue: _i3.Future<List<_i4.Product>>.value(<_i4.Product>[]),
          )
          as _i3.Future<List<_i4.Product>>);

  @override
  _i3.Future<_i4.Product?> getProductById(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#getProductById, [id]),
            returnValue: _i3.Future<_i4.Product?>.value(),
          )
          as _i3.Future<_i4.Product?>);

  @override
  _i3.Future<void> createProduct(_i4.Product? product) =>
      (super.noSuchMethod(
            Invocation.method(#createProduct, [product]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateProduct(_i4.Product? product) =>
      (super.noSuchMethod(
            Invocation.method(#updateProduct, [product]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteProduct(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProduct, [id]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i4.Product>> searchProducts(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#searchProducts, [query]),
            returnValue: _i3.Future<List<_i4.Product>>.value(<_i4.Product>[]),
          )
          as _i3.Future<List<_i4.Product>>);

  @override
  _i3.Future<List<_i4.Product>> getProductsByCategory(String? category) =>
      (super.noSuchMethod(
            Invocation.method(#getProductsByCategory, [category]),
            returnValue: _i3.Future<List<_i4.Product>>.value(<_i4.Product>[]),
          )
          as _i3.Future<List<_i4.Product>>);

  @override
  _i3.Future<List<_i4.Product>> getLowStockProducts() =>
      (super.noSuchMethod(
            Invocation.method(#getLowStockProducts, []),
            returnValue: _i3.Future<List<_i4.Product>>.value(<_i4.Product>[]),
          )
          as _i3.Future<List<_i4.Product>>);
}
