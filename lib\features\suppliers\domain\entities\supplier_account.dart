/// كيان حساب المورد - يمثل المعاملات المحاسبية للمورد
class SupplierAccount {
  final int? id;
  final int supplierId;
  final DateTime transactionDate;
  final String type; // 'purchase_invoice', 'payment_out'
  final double amount;
  final String? description;
  final int? relatedInvoiceId;

  const SupplierAccount({
    this.id,
    required this.supplierId,
    required this.transactionDate,
    required this.type,
    required this.amount,
    this.description,
    this.relatedInvoiceId,
  });

  /// تحديد ما إذا كانت المعاملة مدينة (دين على المورد)
  bool get isDebit => type == 'purchase_invoice';

  /// تحديد ما إذا كانت المعاملة دائنة (دفعة للمورد)
  bool get isCredit => type == 'payment_out';

  /// تحويل الكيان إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'supplierId': supplierId,
      'transactionDate': transactionDate.toIso8601String(),
      'type': type,
      'amount': amount,
      'description': description,
      'relatedInvoiceId': relatedInvoiceId,
    };
  }

  /// إنشاء كيان من Map قادم من قاعدة البيانات
  factory SupplierAccount.fromJson(Map<String, dynamic> json) {
    return SupplierAccount(
      id: json['id']?.toInt(),
      supplierId: json['supplierId']?.toInt() ?? 0,
      transactionDate: DateTime.parse(
        json['transactionDate'] ?? DateTime.now().toIso8601String(),
      ),
      type: json['type'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      description: json['description'],
      relatedInvoiceId: json['relatedInvoiceId']?.toInt(),
    );
  }

  /// نسخ الكيان مع تعديل بعض الخصائص
  SupplierAccount copyWith({
    int? id,
    int? supplierId,
    DateTime? transactionDate,
    String? type,
    double? amount,
    String? description,
    int? relatedInvoiceId,
  }) {
    return SupplierAccount(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      transactionDate: transactionDate ?? this.transactionDate,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupplierAccount &&
        other.id == id &&
        other.supplierId == supplierId &&
        other.transactionDate == transactionDate &&
        other.type == type &&
        other.amount == amount &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      supplierId,
      transactionDate,
      type,
      amount,
      description,
      relatedInvoiceId,
    );
  }

  @override
  String toString() {
    return 'SupplierAccount(id: $id, supplierId: $supplierId, transactionDate: $transactionDate, type: $type, amount: $amount, description: $description, relatedInvoiceId: $relatedInvoiceId)';
  }
}

/// عنصر كشف حساب المورد - يحتوي على المعاملة والرصيد الجاري
class SupplierAccountStatementItem {
  final DateTime date;
  final String description;
  final double? debit;
  final double? credit;
  final double balance;

  const SupplierAccountStatementItem({
    required this.date,
    required this.description,
    this.debit,
    this.credit,
    required this.balance,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupplierAccountStatementItem &&
        other.date == date &&
        other.description == description &&
        other.debit == debit &&
        other.credit == credit &&
        other.balance == balance;
  }

  @override
  int get hashCode {
    return Object.hash(date, description, debit, credit, balance);
  }

  @override
  String toString() {
    return 'SupplierAccountStatementItem(date: $date, description: $description, debit: $debit, credit: $credit, balance: $balance)';
  }
}
