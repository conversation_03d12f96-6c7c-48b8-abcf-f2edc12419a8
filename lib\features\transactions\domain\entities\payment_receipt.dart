class PaymentReceipt {
  final int? id;
  final int? relatedEntityId;
  final String relatedEntityType; // 'customer' أو 'supplier'
  final DateTime transactionDate;
  final double amount;
  final String type; // 'payment_in' (قبض من عميل), 'payment_out' (دفع لمورد)
  final String paymentMethod; // 'cash', 'bank_transfer', 'cheque', etc.
  final String? description;
  final int? relatedInvoiceId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PaymentReceipt({
    this.id,
    this.relatedEntityId,
    required this.relatedEntityType,
    required this.transactionDate,
    required this.amount,
    required this.type,
    required this.paymentMethod,
    this.description,
    this.relatedInvoiceId,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Display name for transaction type
  String get typeDisplayName {
    switch (type) {
      case 'payment_in':
        return 'سند قبض';
      case 'payment_out':
        return 'سند دفع';
      default:
        return 'سند';
    }
  }

  /// Display name for payment method
  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'cash':
        return 'نقدي';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'cheque':
        return 'شيك';
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'debit_card':
        return 'بطاقة خصم';
      default:
        return paymentMethod;
    }
  }

  /// Check if this is a payment received from customer
  bool get isPaymentIn => type == 'payment_in';

  /// Check if this is a payment made to supplier
  bool get isPaymentOut => type == 'payment_out';

  /// Check if related to customer
  bool get isCustomerRelated => relatedEntityType == 'customer';

  /// Check if related to supplier
  bool get isSupplierRelated => relatedEntityType == 'supplier';

  PaymentReceipt copyWith({
    int? id,
    int? relatedEntityId,
    String? relatedEntityType,
    DateTime? transactionDate,
    double? amount,
    String? type,
    String? paymentMethod,
    String? description,
    int? relatedInvoiceId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentReceipt(
      id: id ?? this.id,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      transactionDate: transactionDate ?? this.transactionDate,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PaymentReceipt &&
        other.id == id &&
        other.relatedEntityId == relatedEntityId &&
        other.relatedEntityType == relatedEntityType &&
        other.transactionDate == transactionDate &&
        other.amount == amount &&
        other.type == type &&
        other.paymentMethod == paymentMethod &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        relatedEntityId.hashCode ^
        relatedEntityType.hashCode ^
        transactionDate.hashCode ^
        amount.hashCode ^
        type.hashCode ^
        paymentMethod.hashCode ^
        description.hashCode ^
        relatedInvoiceId.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  @override
  String toString() {
    return 'PaymentReceipt(id: $id, relatedEntityId: $relatedEntityId, relatedEntityType: $relatedEntityType, transactionDate: $transactionDate, amount: $amount, type: $type, paymentMethod: $paymentMethod, description: $description, relatedInvoiceId: $relatedInvoiceId, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
