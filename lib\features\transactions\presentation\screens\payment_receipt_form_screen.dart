import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../../shared_widgets/wrappers.dart';
import '../../../customers/domain/entities/customer.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../suppliers/domain/entities/supplier.dart';
import '../../../suppliers/presentation/providers/supplier_provider.dart';
import '../providers/payment_receipt_provider.dart';

/// شاشة نموذج إنشاء/تعديل سند قبض أو دفع
class PaymentReceiptFormScreen extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const PaymentReceiptFormScreen({super.key, this.arguments});

  @override
  State<PaymentReceiptFormScreen> createState() =>
      _PaymentReceiptFormScreenState();
}

class _PaymentReceiptFormScreenState extends State<PaymentReceiptFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _invoiceIdController = TextEditingController();

  // متغيرات النموذج
  String _selectedEntityType = 'customer'; // customer أو supplier
  int? _selectedEntityId;
  DateTime _selectedDate = DateTime.now();
  String _selectedTransactionType = 'payment_in'; // payment_in أو payment_out
  String _selectedPaymentMethod = 'نقدي';

  // قوائم البيانات
  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];

  // حالة التحميل
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadData();
  }

  /// تهيئة النموذج بالبيانات المرسلة
  void _initializeForm() {
    if (widget.arguments != null) {
      final args = widget.arguments!;

      // تعيين نوع الكيان إذا تم تمريره
      if (args['relatedEntityType'] != null) {
        _selectedEntityType = args['relatedEntityType'];
      }

      // تعيين معرف الكيان إذا تم تمريره
      if (args['relatedEntityId'] != null) {
        _selectedEntityId = args['relatedEntityId'];
      }

      // تعيين نوع المعاملة بناءً على نوع الكيان
      if (_selectedEntityType == 'customer') {
        _selectedTransactionType = 'payment_in'; // قبض من عميل
      } else if (_selectedEntityType == 'supplier') {
        _selectedTransactionType = 'payment_out'; // دفع لمورد
      }
    }
  }

  /// تحميل بيانات العملاء والموردين
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      final supplierProvider = Provider.of<SupplierProvider>(
        context,
        listen: false,
      );

      await Future.wait([
        customerProvider.getAllCustomers(),
        supplierProvider.getAllSuppliers(),
      ]);

      setState(() {
        _customers = customerProvider.customers;
        _suppliers = supplierProvider.suppliers;
      });
    } catch (e) {
      _showErrorSnackBar('فشل في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _invoiceIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'إضافة سند',
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildForm(),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildEntityTypeSection(),
            const SizedBox(height: 16),
            _buildEntitySelectionSection(),
            const SizedBox(height: 16),
            _buildTransactionTypeSection(),
            const SizedBox(height: 16),
            _buildAmountField(),
            const SizedBox(height: 16),
            _buildDateField(),
            const SizedBox(height: 16),
            _buildPaymentMethodField(),
            const SizedBox(height: 16),
            _buildInvoiceIdField(),
            const SizedBox(height: 16),
            _buildDescriptionField(),
            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  /// قسم اختيار نوع الكيان (عميل/مورد)
  Widget _buildEntityTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع الكيان',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('عميل'),
                    value: 'customer',
                    groupValue: _selectedEntityType,
                    onChanged: (value) {
                      setState(() {
                        _selectedEntityType = value!;
                        _selectedEntityId = null; // إعادة تعيين الكيان المحدد
                        _selectedTransactionType = 'payment_in'; // قبض من عميل
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('مورد'),
                    value: 'supplier',
                    groupValue: _selectedEntityType,
                    onChanged: (value) {
                      setState(() {
                        _selectedEntityType = value!;
                        _selectedEntityId = null; // إعادة تعيين الكيان المحدد
                        _selectedTransactionType = 'payment_out'; // دفع لمورد
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم اختيار الكيان المحدد
  Widget _buildEntitySelectionSection() {
    final entities = _selectedEntityType == 'customer'
        ? _customers
        : _suppliers;
    final entityName = _selectedEntityType == 'customer' ? 'العميل' : 'المورد';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار $entityName',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<int>(
              value: _selectedEntityId,
              decoration: InputDecoration(
                labelText: entityName,
                border: const OutlineInputBorder(),
              ),
              items: entities.map((entity) {
                final id = _selectedEntityType == 'customer'
                    ? (entity as Customer).id!
                    : (entity as Supplier).id!;
                final name = _selectedEntityType == 'customer'
                    ? (entity as Customer).name
                    : (entity as Supplier).name;

                return DropdownMenuItem<int>(value: id, child: Text(name));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedEntityId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار $entityName';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  /// قسم نوع المعاملة (قبض/دفع)
  Widget _buildTransactionTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع المعاملة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('قبض'),
                    subtitle: const Text('استلام مبلغ من عميل'),
                    value: 'payment_in',
                    groupValue: _selectedTransactionType,
                    onChanged: _selectedEntityType == 'customer'
                        ? (value) {
                            setState(() {
                              _selectedTransactionType = value!;
                            });
                          }
                        : null,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('دفع'),
                    subtitle: const Text('دفع مبلغ لمورد'),
                    value: 'payment_out',
                    groupValue: _selectedTransactionType,
                    onChanged: _selectedEntityType == 'supplier'
                        ? (value) {
                            setState(() {
                              _selectedTransactionType = value!;
                            });
                          }
                        : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// حقل المبلغ
  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: const InputDecoration(
        labelText: 'المبلغ *',
        suffixText: 'ر.ي',
        border: OutlineInputBorder(),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال المبلغ';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'يرجى إدخال مبلغ صحيح';
        }
        return null;
      },
    );
  }

  /// حقل التاريخ
  Widget _buildDateField() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'تاريخ المعاملة *',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          DateFormat('yyyy/MM/dd').format(_selectedDate),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  /// حقل طريقة الدفع
  Widget _buildPaymentMethodField() {
    final paymentMethods = [
      'نقدي',
      'تحويل بنكي',
      'شيك',
      'بطاقة ائتمان',
      'أخرى',
    ];

    return DropdownButtonFormField<String>(
      value: _selectedPaymentMethod,
      decoration: const InputDecoration(
        labelText: 'طريقة الدفع *',
        border: OutlineInputBorder(),
      ),
      items: paymentMethods.map((method) {
        return DropdownMenuItem<String>(value: method, child: Text(method));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedPaymentMethod = value!;
        });
      },
    );
  }

  /// حقل رقم الفاتورة (اختياري)
  Widget _buildInvoiceIdField() {
    return TextFormField(
      controller: _invoiceIdController,
      decoration: const InputDecoration(
        labelText: 'رقم الفاتورة المرتبطة (اختياري)',
        border: OutlineInputBorder(),
        helperText: 'يمكن ربط السند بفاتورة معينة',
      ),
      keyboardType: TextInputType.number,
    );
  }

  /// حقل الوصف
  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'الوصف (اختياري)',
        border: OutlineInputBorder(),
      ),
      maxLines: 3,
    );
  }

  /// زر الحفظ
  Widget _buildSaveButton() {
    return Consumer<PaymentReceiptProvider>(
      builder: (context, provider, child) {
        return ElevatedButton(
          onPressed: provider.isLoading ? null : _savePaymentReceipt,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: provider.isLoading
              ? const CircularProgressIndicator()
              : const Text('حفظ السند', style: TextStyle(fontSize: 16)),
        );
      },
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// حفظ السند
  Future<void> _savePaymentReceipt() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    final description = _descriptionController.text.trim().isEmpty
        ? null
        : _descriptionController.text.trim();
    final invoiceId = _invoiceIdController.text.trim().isEmpty
        ? null
        : int.tryParse(_invoiceIdController.text.trim());

    final provider = Provider.of<PaymentReceiptProvider>(
      context,
      listen: false,
    );

    final success = await provider.createPaymentReceipt(
      relatedEntityId: _selectedEntityId,
      relatedEntityType: _selectedEntityType,
      transactionDate: _selectedDate,
      amount: amount,
      type: _selectedTransactionType,
      paymentMethod: _selectedPaymentMethod,
      description: description,
      relatedInvoiceId: invoiceId,
    );

    if (mounted) {
      if (success) {
        _showSuccessSnackBar('تم حفظ السند بنجاح');
        Navigator.of(context).pop(true); // إرجاع true للإشارة للنجاح
      } else {
        _showErrorSnackBar(provider.errorMessage ?? 'فشل في حفظ السند');
      }
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
