import '../../data/models/inventory_count_model.dart';

class InventoryCount {
  final int? id;
  final int productId;
  final DateTime countDate;
  final int countedWarehouseQuantity;
  final int countedStoreQuantity;
  final int systemWarehouseQuantity;
  final int systemStoreQuantity;
  final int warehouseDifference;
  final int storeDifference;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const InventoryCount({
    this.id,
    required this.productId,
    required this.countDate,
    required this.countedWarehouseQuantity,
    required this.countedStoreQuantity,
    required this.systemWarehouseQuantity,
    required this.systemStoreQuantity,
    required this.warehouseDifference,
    required this.storeDifference,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  // Create InventoryCount from InventoryCountModel
  factory InventoryCount.fromModel(InventoryCountModel model) {
    return InventoryCount(
      id: model.id,
      productId: model.productId,
      countDate: model.countDate,
      countedWarehouseQuantity: model.countedWarehouseQuantity,
      countedStoreQuantity: model.countedStoreQuantity,
      systemWarehouseQuantity: model.systemWarehouseQuantity,
      systemStoreQuantity: model.systemStoreQuantity,
      warehouseDifference: model.warehouseDifference,
      storeDifference: model.storeDifference,
      notes: model.notes,
    );
  }

  // Factory constructor to create from counted and system quantities
  factory InventoryCount.fromQuantities({
    int? id,
    required int productId,
    required DateTime countDate,
    required int countedWarehouseQuantity,
    required int countedStoreQuantity,
    required int systemWarehouseQuantity,
    required int systemStoreQuantity,
    String? notes,
  }) {
    return InventoryCount(
      id: id,
      productId: productId,
      countDate: countDate,
      countedWarehouseQuantity: countedWarehouseQuantity,
      countedStoreQuantity: countedStoreQuantity,
      systemWarehouseQuantity: systemWarehouseQuantity,
      systemStoreQuantity: systemStoreQuantity,
      warehouseDifference: countedWarehouseQuantity - systemWarehouseQuantity,
      storeDifference: countedStoreQuantity - systemStoreQuantity,
      notes: notes,
    );
  }

  // Business logic methods
  bool get hasWarehouseDifference => warehouseDifference != 0;
  bool get hasStoreDifference => storeDifference != 0;
  bool get hasAnyDifference => hasWarehouseDifference || hasStoreDifference;

  int get totalSystemQuantity => systemWarehouseQuantity + systemStoreQuantity;
  int get totalCountedQuantity =>
      countedWarehouseQuantity + countedStoreQuantity;
  int get totalDifference => totalCountedQuantity - totalSystemQuantity;

  String get warehouseDifferenceText {
    if (warehouseDifference > 0) {
      return '+$warehouseDifference (زيادة)';
    } else if (warehouseDifference < 0) {
      return '$warehouseDifference (نقص)';
    } else {
      return 'لا يوجد فرق';
    }
  }

  String get storeDifferenceText {
    if (storeDifference > 0) {
      return '+$storeDifference (زيادة)';
    } else if (storeDifference < 0) {
      return '$storeDifference (نقص)';
    } else {
      return 'لا يوجد فرق';
    }
  }

  String get statusText {
    if (!hasAnyDifference) {
      return 'متطابق';
    } else if (totalDifference > 0) {
      return 'زيادة في المخزون';
    } else {
      return 'نقص في المخزون';
    }
  }

  String get formattedCountDate {
    return '${countDate.day}/${countDate.month}/${countDate.year}';
  }

  // Additional getters for compatibility
  int get expectedQuantity => systemWarehouseQuantity + systemStoreQuantity;
  int get actualQuantity => countedWarehouseQuantity + countedStoreQuantity;
  int get difference => actualQuantity - expectedQuantity;

  String get status => hasAnyDifference ? 'pending' : 'completed';
  String get statusDisplayName => hasAnyDifference ? 'معلق' : 'مكتمل';

  // Copy with method for updates
  InventoryCount copyWith({
    int? id,
    int? productId,
    DateTime? countDate,
    int? countedWarehouseQuantity,
    int? countedStoreQuantity,
    int? systemWarehouseQuantity,
    int? systemStoreQuantity,
    int? warehouseDifference,
    int? storeDifference,
    String? notes,
  }) {
    return InventoryCount(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      countDate: countDate ?? this.countDate,
      countedWarehouseQuantity:
          countedWarehouseQuantity ?? this.countedWarehouseQuantity,
      countedStoreQuantity: countedStoreQuantity ?? this.countedStoreQuantity,
      systemWarehouseQuantity:
          systemWarehouseQuantity ?? this.systemWarehouseQuantity,
      systemStoreQuantity: systemStoreQuantity ?? this.systemStoreQuantity,
      warehouseDifference: warehouseDifference ?? this.warehouseDifference,
      storeDifference: storeDifference ?? this.storeDifference,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'InventoryCount(id: $id, productId: $productId, countDate: $countDate, '
        'countedWarehouseQuantity: $countedWarehouseQuantity, countedStoreQuantity: $countedStoreQuantity, '
        'systemWarehouseQuantity: $systemWarehouseQuantity, systemStoreQuantity: $systemStoreQuantity, '
        'warehouseDifference: $warehouseDifference, storeDifference: $storeDifference, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryCount &&
        other.id == id &&
        other.productId == productId &&
        other.countDate == countDate &&
        other.countedWarehouseQuantity == countedWarehouseQuantity &&
        other.countedStoreQuantity == countedStoreQuantity &&
        other.systemWarehouseQuantity == systemWarehouseQuantity &&
        other.systemStoreQuantity == systemStoreQuantity &&
        other.warehouseDifference == warehouseDifference &&
        other.storeDifference == storeDifference &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        countDate.hashCode ^
        countedWarehouseQuantity.hashCode ^
        countedStoreQuantity.hashCode ^
        systemWarehouseQuantity.hashCode ^
        systemStoreQuantity.hashCode ^
        warehouseDifference.hashCode ^
        storeDifference.hashCode ^
        notes.hashCode;
  }
}
