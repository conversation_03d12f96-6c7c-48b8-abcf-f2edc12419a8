import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/repositories/product_repository.dart';

class CreateProductUseCase {
  final ProductRepository _repository;

  CreateProductUseCase(this._repository);

  Future<void> call(Product product) async {
    // Validate product data
    if (product.name.trim().isEmpty) {
      throw Exception('Product name cannot be empty');
    }

    if (product.category.trim().isEmpty) {
      throw Exception('Product category cannot be empty');
    }

    if (product.unit.trim().isEmpty) {
      throw Exception('Product unit cannot be empty');
    }

    if (product.lastPurchasePrice != null && product.lastPurchasePrice! < 0) {
      throw Exception('Last purchase price cannot be negative');
    }

    if (product.wholesalePrice < 0) {
      throw Exception('Wholesale price cannot be negative');
    }

    if (product.retailPrice < 0) {
      throw Exception('Retail price cannot be negative');
    }

    if (product.minStockQuantity < 0) {
      throw Exception('Minimum stock quantity cannot be negative');
    }

    if (product.warehouseQuantity < 0) {
      throw Exception('Warehouse quantity cannot be negative');
    }

    if (product.storeQuantity < 0) {
      throw Exception('Store quantity cannot be negative');
    }

    await _repository.createProduct(product);
  }
}
