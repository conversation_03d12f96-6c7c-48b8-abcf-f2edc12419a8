<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market App - دليل التطبيق الشامل</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-right: 4px solid #667eea;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
            padding-right: 20px;
        }
        
        .toc a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #667eea;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px 0;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }
        
        .section h4 {
            color: #555;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        
        .section h5 {
            color: #666;
            font-size: 1.1em;
            margin: 15px 0 8px 0;
        }
        
        .section p {
            margin-bottom: 15px;
            line-height: 1.8;
        }
        
        .section ul, .section ol {
            margin: 15px 0;
            padding-right: 30px;
        }
        
        .section li {
            margin: 8px 0;
            line-height: 1.6;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        
        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #667eea;
        }
        
        .highlight h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
        }
        
        .feature-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .mermaid-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
            color: white;
        }
        
        .stat-card p {
            opacity: 0.9;
            margin: 0;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px 0;
            background: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }
        
        @media print {
            body {
                background: white;
            }
            
            .container {
                box-shadow: none;
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .section {
                page-break-inside: avoid;
            }
            
            .mermaid-container {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏪 Market App</h1>
            <p>دليل التطبيق الشامل - نظام إدارة الأعمال التجارية</p>
            <p style="font-size: 1em; margin-top: 10px;">
                <strong>Flutter/Dart • Clean Architecture • SQLite</strong>
            </p>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h2>📋 جدول المحتويات</h2>
            <ul>
                <li><a href="#overview">🎯 نظرة عامة</a></li>
                <li><a href="#objectives">🎯 الأهداف والمبادئ المحاسبية</a></li>
                <li><a href="#components">🏗️ مكونات التطبيق</a></li>
                <li><a href="#infrastructure">🏗️ البنية التحتية</a></li>
                <li><a href="#dataflow">🔄 تدفق البيانات</a></li>
                <li><a href="#relationships">🔗 العلاقات بين المكونات</a></li>
                <li><a href="#testing">🧪 الفحص والاختبار</a></li>
                <li><a href="#examples">💻 أمثلة الكود</a></li>
                <li><a href="#recommendations">🚀 التوصيات المستقبلية</a></li>
                <li><a href="#conclusion">🎯 الخاتمة</a></li>
            </ul>
        </div>

        <!-- Overview Section -->
        <div class="section" id="overview">
            <h2>🎯 نظرة عامة</h2>
            
            <p><strong>Market App</strong> هو تطبيق شامل لإدارة الأعمال التجارية الصغيرة والمتوسطة، مبني باستخدام Flutter/Dart مع بنية Clean Architecture. يهدف التطبيق إلى توفير حل متكامل لإدارة جميع جوانب العمل التجاري من المبيعات والمشتريات إلى إدارة المخزون والتقارير المالية.</p>

            <div class="highlight">
                <h4>🎯 الهدف الرئيسي</h4>
                <p>توفير نظام محاسبي متكامل يدعم المبادئ المحاسبية الصحيحة مع واجهة مستخدم بديهية وتقارير دقيقة لاتخاذ قرارات مدروسة.</p>
            </div>

            <h3>المواصفات التقنية</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🚀 المنصة</h4>
                    <p>Flutter/Dart (متعدد المنصات)</p>
                </div>
                <div class="feature-card">
                    <h4>🏗️ البنية</h4>
                    <p>Clean Architecture (Presentation, Domain, Data)</p>
                </div>
                <div class="feature-card">
                    <h4>💾 قاعدة البيانات</h4>
                    <p>SQLite مع مكتبة sqflite</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ إدارة الحالة</h4>
                    <p>Provider Pattern</p>
                </div>
                <div class="feature-card">
                    <h4>🔧 حقن التبعيات</h4>
                    <p>GetIt</p>
                </div>
                <div class="feature-card">
                    <h4>🧭 التنقل</h4>
                    <p>GoRouter</p>
                </div>
            </div>

            <h3>المكتبات الرئيسية</h3>
            <div class="code-block">
                <pre>
dependencies:
  flutter:
    sdk: flutter
  sqflite: ^2.3.0
  provider: ^6.1.1
  get_it: ^7.6.4
  go_router: ^12.1.3
  intl: ^0.18.1
  logger: ^2.0.2+1
  workmanager: ^0.5.2
                </pre>
            </div>
        </div>

        <!-- Objectives Section -->
        <div class="section" id="objectives">
            <h2>🎯 الأهداف والمبادئ المحاسبية</h2>
            
            <h3>الأهداف الرئيسية</h3>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💰 إدارة المبيعات والمشتريات</h4>
                    <ul>
                        <li>تسجيل فواتير المبيعات والمشتريات</li>
                        <li>دعم الدفعات الجزئية</li>
                        <li>تتبع حالة الدفع</li>
                        <li>ربط الفواتير بالعملاء والموردين</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>👥 إدارة أرصدة العملاء والموردين</h4>
                    <ul>
                        <li>حساب الأرصدة ديناميكياً</li>
                        <li>كشوفات الحسابات التفصيلية</li>
                        <li>إدارة سندات القبض والدفع</li>
                        <li>تتبع المستحقات</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📦 إدارة المخزون المتقدمة</h4>
                    <ul>
                        <li>نظام FIFO لدفعات الشراء</li>
                        <li>تتبع المحل والمستودع منفصلاً</li>
                        <li>التحويلات الداخلية</li>
                        <li>عمليات الجرد والتسوية</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📊 التقارير المالية الدقيقة</h4>
                    <ul>
                        <li>حساب الأرباح بناءً على FIFO</li>
                        <li>تقارير الإيرادات والمصروفات</li>
                        <li>تحليل الأداء المالي</li>
                        <li>مؤشرات الأداء الرئيسية</li>
                    </ul>
                </div>
            </div>

            <h3>المبادئ المحاسبية المطبقة</h3>
            
            <div class="highlight">
                <h4>📈 مبدأ الاستحقاق (Accrual Principle)</h4>
                <p>تسجيل المبيعات عند حدوثها وليس عند الدفع، مما يضمن دقة التقارير المالية.</p>
            </div>
            
            <div class="highlight">
                <h4>⚖️ مبدأ مطابقة الإيرادات والمصروفات</h4>
                <p>ربط تكلفة البضاعة المباعة بالإيرادات في نفس الفترة باستخدام نظام FIFO.</p>
            </div>
            
            <div class="highlight">
                <h4>🛡️ مبدأ الحيطة والحذر</h4>
                <p>التحقق من صحة البيانات واستخدام المعاملات لضمان سلامة البيانات.</p>
            </div>
        </div>

        <!-- Data Flow Section -->
        <div class="section" id="dataflow">
            <h2>🔄 تدفق البيانات</h2>
            
            <h3>مخطط تدفق البيانات العام</h3>
            <div class="mermaid-container">
                <div class="mermaid">
                graph TD
                    A[Presentation Layer] --> B[Domain Layer]
                    B --> C[Data Layer]
                    C --> D[SQLite Database]
                    
                    A1[SaleFormScreen] --> B1[CreateSaleUseCase]
                    B1 --> C1[SaleRepository]
                    C1 --> D1[SaleDatabaseService]
                    
                    D1 --> D2[Database Transaction]
                    D2 --> D3[Update Inventory]
                    D2 --> D4[Update Customer Account]
                    D2 --> D5[Create Activity Log]
                </div>
            </div>

            <h3>تدفق حساب رصيد العميل</h3>
            <div class="mermaid-container">
                <div class="mermaid">
                sequenceDiagram
                    participant UI as Customer Screen
                    participant CP as CustomerProvider
                    participant UC as GetCustomerBalanceUseCase
                    participant CR as CustomerRepository
                    participant DB as Database
                    
                    UI->>CP: fetchCustomerBalance(customerId)
                    CP->>UC: call(customerId)
                    UC->>CR: getCustomerAccountStatement(customerId)
                    CR->>DB: SELECT * FROM customer_accounts WHERE customerId = ?
                    DB-->>CR: List<CustomerAccount>
                    CR-->>UC: List<CustomerAccount>
                    UC->>UC: calculateRunningBalance()
                    UC-->>CP: CustomerBalance
                    CP->>CP: notifyListeners()
                    CP-->>UI: Updated UI
                </div>
            </div>
        </div>

        <!-- Database Relationships Section -->
        <div class="section" id="relationships">
            <h2>🔗 العلاقات بين المكونات</h2>
            
            <h3>العلاقات في قاعدة البيانات</h3>
            <div class="mermaid-container">
                <div class="mermaid">
                erDiagram
                    CUSTOMERS ||--o{ SALES : "has many"
                    CUSTOMERS ||--o{ CUSTOMER_ACCOUNTS : "has many"
                    SUPPLIERS ||--o{ PURCHASES : "has many"
                    SUPPLIERS ||--o{ SUPPLIER_ACCOUNTS : "has many"
                    SALES ||--o{ SALE_ITEMS : "contains"
                    PURCHASES ||--o{ PURCHASE_ITEMS : "contains"
                    PRODUCTS ||--o{ SALE_ITEMS : "sold in"
                    PRODUCTS ||--o{ PURCHASE_ITEMS : "bought in"
                    PRODUCTS ||--o{ PURCHASE_BATCHES : "has batches"
                    CATEGORIES ||--o{ PRODUCTS : "categorizes"
                    
                    CUSTOMERS {
                        int id PK
                        string name
                        string phone
                        string email
                        double currentBalance
                    }
                    
                    SALES {
                        int id PK
                        int customerId FK
                        datetime saleDate
                        double totalAmount
                        double totalPaidAmount
                        string status
                    }
                    
                    PRODUCTS {
                        int id PK
                        string name
                        int categoryId FK
                        double retailPrice
                        int storeQuantity
                        int warehouseQuantity
                    }
                </div>
            </div>
        </div>

        <!-- Performance Statistics -->
        <div class="section">
            <h2>📊 إحصائيات الأداء المتوقعة</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>40%</h3>
                    <p>تحسين الكفاءة التشغيلية</p>
                </div>
                <div class="stat-card">
                    <h3>95%+</h3>
                    <p>دقة في التقارير المالية</p>
                </div>
                <div class="stat-card">
                    <h3>60%</h3>
                    <p>توفير الوقت في العمليات</p>
                </div>
                <div class="stat-card">
                    <h3>80%</h3>
                    <p>تقليل الأخطاء البشرية</p>
                </div>
            </div>
        </div>

        <!-- Database Tables -->
        <div class="section">
            <h2>🗄️ هيكل قاعدة البيانات</h2>
            
            <h3>الجداول الرئيسية</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الجدول</th>
                            <th>الوصف</th>
                            <th>الحقول الرئيسية</th>
                            <th>العلاقات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>sales</td>
                            <td>فواتير المبيعات</td>
                            <td>id, customerId, totalAmount, status</td>
                            <td>customers, sale_items</td>
                        </tr>
                        <tr>
                            <td>purchases</td>
                            <td>فواتير المشتريات</td>
                            <td>id, supplierId, totalAmount, status</td>
                            <td>suppliers, purchase_items</td>
                        </tr>
                        <tr>
                            <td>products</td>
                            <td>المنتجات</td>
                            <td>id, name, retailPrice, storeQuantity</td>
                            <td>categories, sale_items, purchase_items</td>
                        </tr>
                        <tr>
                            <td>customers</td>
                            <td>العملاء</td>
                            <td>id, name, phone, currentBalance</td>
                            <td>sales, customer_accounts</td>
                        </tr>
                        <tr>
                            <td>suppliers</td>
                            <td>الموردين</td>
                            <td>id, name, phone, currentBalance</td>
                            <td>purchases, supplier_accounts</td>
                        </tr>
                        <tr>
                            <td>purchase_batches</td>
                            <td>دفعات الشراء (FIFO)</td>
                            <td>id, productId, quantity, unitPurchasePrice</td>
                            <td>products</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Conclusion -->
        <div class="section" id="conclusion">
            <h2>🎯 الخاتمة</h2>
            
            <p><strong>Market App</strong> يمثل حلاً شاملاً ومتطوراً لإدارة الأعمال التجارية الصغيرة والمتوسطة. بفضل بنيته المعمارية النظيفة واستخدام أحدث التقنيات، يوفر التطبيق نظاماً محاسبياً دقيقاً وواجهة مستخدم بديهية.</p>

            <div class="highlight">
                <h4>🏆 المزايا الرئيسية</h4>
                <ul>
                    <li><strong>دقة محاسبية عالية</strong> مع نظام FIFO لإدارة المخزون</li>
                    <li><strong>واجهة مستخدم بديهية</strong> تدعم اللغة العربية</li>
                    <li><strong>تقارير مالية شاملة</strong> لاتخاذ قرارات مدروسة</li>
                    <li><strong>أمان البيانات</strong> مع النسخ الاحتياطي التلقائي</li>
                    <li><strong>قابلية التوسع</strong> لنمو الأعمال</li>
                </ul>
            </div>

            <p>يعتبر هذا التطبيق استثماراً استراتيجياً لأي عمل تجاري يسعى للنمو والتطور في العصر الرقمي، مع ضمان الحفاظ على أعلى معايير الجودة والأمان.</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>تاريخ الإنشاء:</strong> ديسمبر 2024</p>
            <p><strong>الإصدار:</strong> 1.0</p>
            <p><strong>المؤلف:</strong> فريق تطوير Market App</p>
            <p style="margin-top: 15px; font-style: italic;">
                تم إعداد هذا التوثيق ليكون مرجعاً شاملاً للمطورين وأصحاب المصلحة
            </p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            },
            er: {
                useMaxWidth: true
            }
        });
    </script>
</body>
</html>
