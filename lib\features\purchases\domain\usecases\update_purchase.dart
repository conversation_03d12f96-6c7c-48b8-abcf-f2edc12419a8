import '../entities/purchase.dart';
import '../repositories/purchase_repository.dart';

class UpdatePurchaseUseCase {
  final PurchaseRepository _repository;

  UpdatePurchaseUseCase(this._repository);

  Future<void> call(Purchase purchase) async {
    // Validation
    if (purchase.id == null || purchase.id! <= 0) {
      throw Exception('Purchase ID must be provided and greater than 0');
    }

    if (purchase.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (purchase.totalPaidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (purchase.totalPaidAmount > purchase.totalAmount) {
      throw Exception('Paid amount cannot exceed total amount');
    }

    if (purchase.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (purchase.status.isEmpty) {
      throw Exception('Purchase status cannot be empty');
    }

    // Validate payment amounts - dueAmount is now calculated automatically
    // No need for additional validation since dueAmount = totalAmount - totalPaidAmount

    try {
      await _repository.updatePurchase(purchase);
    } catch (e) {
      throw Exception('Failed to update purchase: $e');
    }
  }
}
