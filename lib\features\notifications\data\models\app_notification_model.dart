class AppNotificationModel {
  final int? id;
  final String type;
  final String message;
  final DateTime date;
  final int? relatedEntityId;
  final String? relatedEntityType;
  final String? suggestedAction;
  final String? suggestedActionRoute;
  final bool isRead;
  final bool isActionTaken;

  const AppNotificationModel({
    this.id,
    required this.type,
    required this.message,
    required this.date,
    this.relatedEntityId,
    this.relatedEntityType,
    this.suggestedAction,
    this.suggestedActionRoute,
    this.isRead = false,
    this.isActionTaken = false,
  });

  // Convert from Map (from database)
  factory AppNotificationModel.fromMap(Map<String, dynamic> map) {
    return AppNotificationModel(
      id: map['id'] as int?,
      type: map['type'] as String,
      message: map['message'] as String,
      date: DateTime.parse(map['date'] as String),
      relatedEntityId: map['relatedEntityId'] as int?,
      relatedEntityType: map['relatedEntityType'] as String?,
      suggestedAction: map['suggestedAction'] as String?,
      suggestedActionRoute: map['suggestedActionRoute'] as String?,
      isRead: (map['isRead'] as int) == 1,
      isActionTaken: (map['isActionTaken'] as int) == 1,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'message': message,
      'date': date.toIso8601String(),
      'relatedEntityId': relatedEntityId,
      'relatedEntityType': relatedEntityType,
      'suggestedAction': suggestedAction,
      'suggestedActionRoute': suggestedActionRoute,
      'isRead': isRead ? 1 : 0,
      'isActionTaken': isActionTaken ? 1 : 0,
    };
  }

  // Copy with method for updates
  AppNotificationModel copyWith({
    int? id,
    String? type,
    String? message,
    DateTime? date,
    int? relatedEntityId,
    String? relatedEntityType,
    String? suggestedAction,
    String? suggestedActionRoute,
    bool? isRead,
    bool? isActionTaken,
  }) {
    return AppNotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      date: date ?? this.date,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      suggestedAction: suggestedAction ?? this.suggestedAction,
      suggestedActionRoute: suggestedActionRoute ?? this.suggestedActionRoute,
      isRead: isRead ?? this.isRead,
      isActionTaken: isActionTaken ?? this.isActionTaken,
    );
  }

  @override
  String toString() {
    return 'AppNotificationModel(id: $id, type: $type, message: $message, '
        'date: $date, relatedEntityId: $relatedEntityId, '
        'relatedEntityType: $relatedEntityType, suggestedAction: $suggestedAction, '
        'suggestedActionRoute: $suggestedActionRoute, isRead: $isRead, '
        'isActionTaken: $isActionTaken)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotificationModel &&
        other.id == id &&
        other.type == type &&
        other.message == message &&
        other.date == date &&
        other.relatedEntityId == relatedEntityId &&
        other.relatedEntityType == relatedEntityType &&
        other.suggestedAction == suggestedAction &&
        other.suggestedActionRoute == suggestedActionRoute &&
        other.isRead == isRead &&
        other.isActionTaken == isActionTaken;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        message.hashCode ^
        date.hashCode ^
        relatedEntityId.hashCode ^
        relatedEntityType.hashCode ^
        suggestedAction.hashCode ^
        suggestedActionRoute.hashCode ^
        isRead.hashCode ^
        isActionTaken.hashCode;
  }
}
