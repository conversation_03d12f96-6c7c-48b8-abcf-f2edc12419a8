import '../../data/models/purchase_model.dart';

class Purchase {
  final int? id;
  final int? supplierId;
  final DateTime purchaseDate;
  final double totalAmount;
  final double discountAmount;
  final double totalPaidAmount;
  final String paymentMethod;
  final String? notes;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Purchase({
    this.id,
    this.supplierId,
    required this.purchaseDate,
    required this.totalAmount,
    this.discountAmount = 0.0,
    this.totalPaidAmount = 0.0,
    required this.paymentMethod,
    this.notes,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  // Create Purchase from PurchaseModel
  factory Purchase.fromModel(PurchaseModel model) {
    return Purchase(
      id: model.id,
      supplierId: model.supplierId,
      purchaseDate: model.purchaseDate,
      totalAmount: model.totalAmount,
      discountAmount: model.discountAmount,
      totalPaidAmount: model.totalPaidAmount,
      paymentMethod: model.paymentMethod,
      notes: model.notes,
      status: model.status,
    );
  }

  // Computed properties
  double get netAmount => totalAmount - discountAmount;
  double get dueAmount => netAmount - totalPaidAmount;

  // Business logic methods
  bool get isFullyPaid => dueAmount <= 0;
  bool get isPartiallyPaid => totalPaidAmount > 0 && dueAmount > 0;
  bool get isUnpaid => totalPaidAmount <= 0;
  bool get isCash => paymentMethod == 'cash';
  bool get isCredit => paymentMethod == 'credit';
  bool get isBankTransfer => paymentMethod == 'bank_transfer';

  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'cash':
        return 'نقدي';
      case 'credit':
        return 'آجل';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return paymentMethod;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد الانتظار';
      case 'pending_payment':
        return 'في انتظار الدفع';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  String get paymentStatusText {
    if (isFullyPaid) return 'مدفوعة بالكامل';
    if (isPartiallyPaid) return 'مدفوعة جزئياً';
    return 'غير مدفوعة';
  }

  String get paymentStatus {
    if (isFullyPaid) return 'paid';
    if (isPartiallyPaid) return 'partial';
    return 'unpaid';
  }

  // Copy with method for updates
  Purchase copyWith({
    int? id,
    int? supplierId,
    DateTime? purchaseDate,
    double? totalAmount,
    double? discountAmount,
    double? totalPaidAmount,
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return Purchase(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalPaidAmount: totalPaidAmount ?? this.totalPaidAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'Purchase(id: $id, supplierId: $supplierId, purchaseDate: $purchaseDate, '
        'totalAmount: $totalAmount, totalPaidAmount: $totalPaidAmount, dueAmount: $dueAmount, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Purchase &&
        other.id == id &&
        other.supplierId == supplierId &&
        other.purchaseDate == purchaseDate &&
        other.totalAmount == totalAmount &&
        other.totalPaidAmount == totalPaidAmount &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        supplierId.hashCode ^
        purchaseDate.hashCode ^
        totalAmount.hashCode ^
        totalPaidAmount.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
