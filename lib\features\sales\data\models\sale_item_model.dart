class SaleItemModel {
  final int? id;
  final int saleId;
  final int? productId;
  final int? quantity;
  final double unitPrice;
  final String itemType; // 'wholesale_product' or 'retail_goods_summary'
  final String? description;
  final String? unit; // الوحدة (كيلو، قطعة، متر، إلخ)

  const SaleItemModel({
    this.id,
    required this.saleId,
    this.productId,
    this.quantity,
    required this.unitPrice,
    required this.itemType,
    this.description,
    this.unit,
  });

  // Convert from Map (from database)
  factory SaleItemModel.fromMap(Map<String, dynamic> map) {
    return SaleItemModel(
      id: map['id'] as int?,
      saleId: map['saleId'] as int,
      productId: map['productId'] as int?,
      quantity: map['quantity'] as int?,
      unitPrice: (map['unitPrice'] as num).toDouble(),
      itemType: map['itemType'] as String,
      description: map['description'] as String?,
      unit: map['unit'] as String?,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'saleId': saleId,
      'productId': productId,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'itemType': itemType,
      'description': description,
      'unit': unit,
    };
  }

  // Business logic methods
  double get totalPrice => (quantity ?? 1) * unitPrice;

  // تحديد نوع البند - المنتجات العادية من المخزون
  bool get isWholesaleProduct => itemType == 'product';
  // بنود الخدمات أو ملخصات البضاعة التي لا تؤثر على المخزون
  bool get isRetailGoodsSummary => itemType == 'service';

  // Copy with method for updates
  SaleItemModel copyWith({
    int? id,
    int? saleId,
    int? productId,
    int? quantity,
    double? unitPrice,
    String? itemType,
    String? description,
    String? unit,
  }) {
    return SaleItemModel(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      itemType: itemType ?? this.itemType,
      description: description ?? this.description,
      unit: unit ?? this.unit,
    );
  }

  @override
  String toString() {
    return 'SaleItemModel(id: $id, saleId: $saleId, productId: $productId, '
        'quantity: $quantity, unitPrice: $unitPrice, itemType: $itemType, '
        'description: $description, unit: $unit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SaleItemModel &&
        other.id == id &&
        other.saleId == saleId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.itemType == itemType &&
        other.description == description &&
        other.unit == unit;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        saleId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode ^
        itemType.hashCode ^
        description.hashCode ^
        unit.hashCode;
  }
}
