import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:workmanager/workmanager.dart'; // Disabled temporarily
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:market/core/database/database_service.dart';

class BackupService {
  static BackupService? _instance;
  static BackupService get instance => _instance ??= BackupService._();
  BackupService._();

  // static const String _backupTaskName = 'automaticBackup'; // غير مستخدم حالياً
  static const String _localBackupFolder = 'market_backups';

  // Google Drive API credentials (you'll need to set these up)
  static const String _googleDriveClientId = 'YOUR_CLIENT_ID';
  static const String _googleDriveClientSecret = 'YOUR_CLIENT_SECRET';
  static const List<String> _googleDriveScopes = [
    drive.DriveApi.driveFileScope,
  ];

  /// Perform local backup of the database
  Future<String> performLocalBackup() async {
    try {
      // Request storage permission
      final permission = await Permission.storage.request();
      if (!permission.isGranted) {
        throw Exception('تم رفض إذن الوصول للتخزين');
      }

      // Get database file path
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        throw Exception('ملف قاعدة البيانات غير موجود');
      }

      // Create backup directory
      final backupDir = await _getLocalBackupDirectory();
      await backupDir.create(recursive: true);

      // Create backup file with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupFileName = 'market_backup_$timestamp.db';
      final backupFile = File(path.join(backupDir.path, backupFileName));

      // Copy database file to backup location
      await dbFile.copy(backupFile.path);

      // Clean old backups (keep only last 10)
      await _cleanOldBackups(backupDir);

      return backupFile.path;
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية المحلية: $e');
    }
  }

  /// Restore database from local backup
  Future<void> restoreLocalBackup(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // Get current database path first
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      // Close database connection
      await db.close();

      // Create backup of current database before restore
      if (await dbFile.exists()) {
        final currentBackupPath =
            '${dbPath}_before_restore_${DateTime.now().millisecondsSinceEpoch}';
        await dbFile.copy(currentBackupPath);
      }

      // Copy backup file to database location
      await backupFile.copy(dbPath);

      // Reinitialize database
      await DatabaseService.instance.database;
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية المحلية: $e');
    }
  }

  /// Get list of local backup files
  Future<List<FileSystemEntity>> getLocalBackups() async {
    try {
      final backupDir = await _getLocalBackupDirectory();
      if (!await backupDir.exists()) {
        return [];
      }

      final backups = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.db'))
          .toList();

      // Sort by modification date (newest first)
      backups.sort(
        (a, b) => File(
          b.path,
        ).lastModifiedSync().compareTo(File(a.path).lastModifiedSync()),
      );

      return backups;
    } catch (e) {
      debugPrint('خطأ في جلب النسخ الاحتياطية المحلية: $e');
      return [];
    }
  }

  /// Perform Google Drive backup
  Future<String> performGoogleDriveBackup() async {
    try {
      // Check if credentials are configured
      if (_googleDriveClientId == 'YOUR_CLIENT_ID' ||
          _googleDriveClientSecret == 'YOUR_CLIENT_SECRET') {
        throw Exception('يجب تكوين بيانات اعتماد Google Drive أولاً');
      }

      // Get database file
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        throw Exception('ملف قاعدة البيانات غير موجود');
      }

      // Create credentials
      final credentials = ClientId(
        _googleDriveClientId,
        _googleDriveClientSecret,
      );

      // Authenticate with Google Drive
      final client = await clientViaUserConsent(
        credentials,
        _googleDriveScopes,
        (url) {
          // This would typically open a browser for authentication
          // For now, we'll throw an exception with instructions
          throw Exception('يرجى فتح الرابط التالي في المتصفح للمصادقة: $url');
        },
      );

      final driveApi = drive.DriveApi(client);

      // Create backup file name with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupFileName = 'market_backup_$timestamp.db';

      // Create file metadata
      final fileMetadata = drive.File()
        ..name = backupFileName
        ..parents = ['appDataFolder']; // Store in app data folder

      // Upload file
      final media = drive.Media(dbFile.openRead(), dbFile.lengthSync());
      final uploadedFile = await driveApi.files.create(
        fileMetadata,
        uploadMedia: media,
      );

      client.close();

      return uploadedFile.id ?? 'unknown_id';
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية على Google Drive: $e');
    }
  }

  /// Restore from Google Drive backup
  Future<void> restoreGoogleDriveBackup(String backupId) async {
    try {
      // Check if credentials are configured
      if (_googleDriveClientId == 'YOUR_CLIENT_ID' ||
          _googleDriveClientSecret == 'YOUR_CLIENT_SECRET') {
        throw Exception('يجب تكوين بيانات اعتماد Google Drive أولاً');
      }

      // Create credentials
      final credentials = ClientId(
        _googleDriveClientId,
        _googleDriveClientSecret,
      );

      // Authenticate with Google Drive
      final client = await clientViaUserConsent(
        credentials,
        _googleDriveScopes,
        (url) {
          throw Exception('يرجى فتح الرابط التالي في المتصفح للمصادقة: $url');
        },
      );

      final driveApi = drive.DriveApi(client);

      // Download backup file
      final media = await driveApi.files.get(
        backupId,
        downloadOptions: drive.DownloadOptions.fullMedia,
      );

      if (media is! drive.Media) {
        throw Exception('فشل في تحميل ملف النسخة الاحتياطية');
      }

      // Get current database path
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      // Close database connection
      await db.close();

      // Create backup of current database before restore
      if (await dbFile.exists()) {
        final currentBackupPath =
            '${dbPath}_before_restore_${DateTime.now().millisecondsSinceEpoch}';
        await dbFile.copy(currentBackupPath);
      }

      // Write downloaded data to database file
      final downloadedData = <int>[];
      await for (final chunk in media.stream) {
        downloadedData.addAll(chunk);
      }

      await dbFile.writeAsBytes(downloadedData);

      client.close();

      // Reinitialize database
      await DatabaseService.instance.database;
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية من Google Drive: $e');
    }
  }

  /// Get list of Google Drive backup files
  Future<List<drive.File>> getGoogleDriveBackups() async {
    try {
      // Check if credentials are configured
      if (_googleDriveClientId == 'YOUR_CLIENT_ID' ||
          _googleDriveClientSecret == 'YOUR_CLIENT_SECRET') {
        throw Exception('يجب تكوين بيانات اعتماد Google Drive أولاً');
      }

      // Create credentials
      final credentials = ClientId(
        _googleDriveClientId,
        _googleDriveClientSecret,
      );

      // Authenticate with Google Drive
      final client = await clientViaUserConsent(
        credentials,
        _googleDriveScopes,
        (url) {
          throw Exception('يرجى فتح الرابط التالي في المتصفح للمصادقة: $url');
        },
      );

      final driveApi = drive.DriveApi(client);

      // Search for backup files in app data folder
      final fileList = await driveApi.files.list(
        q: "parents in 'appDataFolder' and name contains 'market_backup'",
        orderBy: 'createdTime desc',
        pageSize: 50,
      );

      client.close();

      return fileList.files ?? [];
    } catch (e) {
      throw Exception('فشل في جلب قائمة النسخ الاحتياطية من Google Drive: $e');
    }
  }

  /// Setup automatic backup (disabled temporarily)
  Future<void> setupAutomaticBackup({
    bool enabled = true,
    Duration frequency = const Duration(days: 1),
  }) async {
    try {
      // Workmanager functionality disabled temporarily due to compatibility issues
      debugPrint('النسخ الاحتياطي التلقائي معطل مؤقتاً');
    } catch (e) {
      debugPrint('خطأ في إعداد النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// Initialize Workmanager for background tasks (disabled temporarily)
  static Future<void> initializeWorkmanager() async {
    try {
      // Workmanager initialization disabled temporarily
      debugPrint('تهيئة Workmanager معطلة مؤقتاً');
    } catch (e) {
      debugPrint('خطأ في تهيئة Workmanager: $e');
    }
  }

  /// Get local backup directory
  Future<Directory> _getLocalBackupDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(documentsDir.path, _localBackupFolder));
  }

  /// Clean old backup files (keep only last 10)
  Future<void> _cleanOldBackups(Directory backupDir) async {
    try {
      final backups = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.db'))
          .toList();

      if (backups.length <= 10) return;

      // Sort by modification date (oldest first)
      backups.sort(
        (a, b) => File(
          a.path,
        ).lastModifiedSync().compareTo(File(b.path).lastModifiedSync()),
      );

      // Delete oldest backups (keep only last 10)
      for (int i = 0; i < backups.length - 10; i++) {
        await backups[i].delete();
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
}

/// Background task callback for automatic backup (disabled temporarily)
@pragma('vm:entry-point')
void callbackDispatcher() {
  // Workmanager functionality disabled temporarily
  debugPrint('مهمة النسخ الاحتياطي الخلفية معطلة مؤقتاً');
}
