import '../entities/customer.dart';
import '../repositories/customer_repository.dart';

class GetCustomerById {
  final CustomerRepository _repository;

  GetCustomerById(this._repository);

  Future<Customer?> call(int id) async {
    if (id <= 0) {
      throw Exception('Invalid customer ID');
    }

    try {
      return await _repository.getCustomerById(id);
    } catch (e) {
      throw Exception('Failed to get customer by id: $e');
    }
  }
}
