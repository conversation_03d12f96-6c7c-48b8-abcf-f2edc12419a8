import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';
import '../../../../core/widgets/date_range_picker_widget.dart';

class SupplierAccountStatementDetailsScreen extends StatefulWidget {
  final int supplierId;

  const SupplierAccountStatementDetailsScreen({
    super.key,
    required this.supplierId,
  });

  @override
  State<SupplierAccountStatementDetailsScreen> createState() =>
      _SupplierAccountStatementDetailsScreenState();
}

class _SupplierAccountStatementDetailsScreenState
    extends State<SupplierAccountStatementDetailsScreen> {
  Supplier? _supplier;
  bool _isLoadingSupplier = true;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _showDatePicker = false;

  @override
  void initState() {
    super.initState();
    print('🔍 DEBUG: initState called for supplierId: ${widget.supplierId}');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('🔍 DEBUG: addPostFrameCallback triggered');
      _loadData();
    });
  }

  Future<void> _loadData() async {
    print('🔍 DEBUG: _loadData called for supplierId: ${widget.supplierId}');
    final provider = context.read<SupplierProvider>();

    // تحميل بيانات المورد
    setState(() => _isLoadingSupplier = true);
    try {
      _supplier = await provider.getSupplierById(widget.supplierId);
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => _isLoadingSupplier = false);
      }
    }

    // تحميل كشف الحساب مع المدة الزمنية
    print('🔍 DEBUG: About to call provider.fetchSupplierAccountStatement');
    await provider.fetchSupplierAccountStatement(
      widget.supplierId,
      fromDate: _startDate,
      toDate: _endDate,
    );
    print('✅ DEBUG: provider.fetchSupplierAccountStatement completed');
  }

  void _onDateRangeChanged(DateTime? startDate, DateTime? endDate) {
    setState(() {
      _startDate = startDate;
      _endDate = endDate;
    });
    _loadData(); // إعادة تحميل البيانات عند تغيير المدة
  }

  Future<void> _exportToPdf() async {
    try {
      final provider = context.read<SupplierProvider>();
      if (provider.statementItems.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('لا توجد بيانات للتصدير')));
        return;
      }

      // TODO: تنفيذ تصدير كشف الحساب إلى PDF
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('سيتم تنفيذ التصدير قريباً')),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('فشل في التصدير: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _supplier?.name ?? 'كشف حساب المورد',
      actions: [
        // زر تبديل عرض اختيار المدة
        IconButton(
          onPressed: () {
            setState(() {
              _showDatePicker = !_showDatePicker;
            });
          },
          icon: Icon(_showDatePicker ? Icons.expand_less : Icons.expand_more),
          tooltip: 'تحديد المدة الزمنية',
        ),
        // زر التصدير
        IconButton(
          onPressed: _exportToPdf,
          icon: const Icon(Icons.picture_as_pdf),
          tooltip: 'تصدير PDF',
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await context.push<bool>(
            '/transactions/receipts/new',
            extra: {
              'relatedEntityType': 'supplier',
              'relatedEntityId': widget.supplierId,
            },
          );

          // إعادة تحميل البيانات إذا تم إنشاء سند بنجاح
          if (result == true && mounted) {
            await _loadData();
          }
        },
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: RefreshIndicator(
        onRefresh: _loadData,
        child: Consumer<SupplierProvider>(
          builder: (context, provider, child) {
            if (_isLoadingSupplier || provider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (provider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      provider.errorMessage!,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            if (provider.statementItems.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.receipt_long_outlined,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد معاملات لهذا المورد حتى الآن',
                      style: TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // اختيار المدة الزمنية (قابل للطي)
                if (_showDatePicker)
                  DateRangePickerWidget(
                    initialStartDate: _startDate,
                    initialEndDate: _endDate,
                    onDateRangeChanged: _onDateRangeChanged,
                    title: 'تحديد مدة كشف الحساب',
                  ),

                // رأس الكشف (Header)
                _buildAccountSummaryCard(provider.statementItems),
                const SizedBox(height: 16),

                // رأس الجدول
                _buildTableHeader(),
                const SizedBox(height: 8),

                // قائمة المعاملات
                Expanded(
                  child: _buildTransactionsList(provider.statementItems),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildAccountSummaryCard(List<dynamic> items) {
    print(
      '🔍 DEBUG: Building summary card for supplier. Items count: ${items.length}',
    );

    // حساب الإجماليات
    double totalDebits = 0.0;
    double totalCredits = 0.0;
    double finalBalance = 0.0;

    for (final item in items) {
      totalDebits += item.debit ?? 0.0;
      totalCredits += item.credit ?? 0.0;
    }

    if (items.isNotEmpty) {
      finalBalance = items.first.balance; // الأحدث أولاً
      print('📊 DEBUG: First item balance: ${items.first.balance}');
      print(
        '📊 DEBUG: Total debits: $totalDebits, Total credits: $totalCredits, Final balance: $finalBalance',
      );
    } else {
      print('⚠️ DEBUG: No items found for supplier statement');
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم المورد
            Text(
              _supplier?.name ?? 'مورد غير معروف',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // الملخص المالي
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الديون',
                    totalDebits,
                    Colors.orange,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الدفعات',
                    totalCredits,
                    Colors.green,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // الرصيد النهائي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: finalBalance > 0
                    ? Colors.orange.shade50
                    : finalBalance < 0
                    ? Colors.green.shade50
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: finalBalance > 0
                      ? Colors.orange.shade200
                      : finalBalance < 0
                      ? Colors.green.shade200
                      : Colors.grey.shade200,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الرصيد النهائي:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${finalBalance.toStringAsFixed(2)} ر.ي',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: finalBalance > 0
                          ? Colors.orange.shade700
                          : finalBalance < 0
                          ? Colors.green.shade700
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ر.ي',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'التاريخ',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'البيان',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'مدين',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'دائن',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'الرصيد',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(List<dynamic> items) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildTransactionCard(item);
      },
    );
  }

  Widget _buildTransactionCard(dynamic item) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            // التاريخ
            Expanded(
              flex: 2,
              child: Text(
                dateFormat.format(item.date),
                style: const TextStyle(fontSize: 12),
              ),
            ),

            // البيان
            Expanded(
              flex: 3,
              child: Text(
                item.description,
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // مدين
            Expanded(
              flex: 2,
              child: Text(
                (item.debit ?? 0.0) > 0
                    ? (item.debit ?? 0.0).toStringAsFixed(2)
                    : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // دائن
            Expanded(
              flex: 2,
              child: Text(
                (item.credit ?? 0.0) > 0
                    ? (item.credit ?? 0.0).toStringAsFixed(2)
                    : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // الرصيد
            Expanded(
              flex: 2,
              child: Text(
                item.balance.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: item.balance > 0
                      ? Colors.orange.shade700
                      : item.balance < 0
                      ? Colors.green.shade700
                      : Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
