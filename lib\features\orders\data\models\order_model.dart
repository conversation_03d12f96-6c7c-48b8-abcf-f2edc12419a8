class OrderModel {
  final int? id;
  final DateTime orderDate;
  final double totalEstimatedCost;
  final String? notes;
  final String status;

  const OrderModel({
    this.id,
    required this.orderDate,
    required this.totalEstimatedCost,
    this.notes,
    required this.status,
  });

  // Convert from Map (from database)
  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      id: map['id'] as int?,
      orderDate: DateTime.parse(map['orderDate'] as String),
      totalEstimatedCost: (map['totalEstimatedCost'] as num).toDouble(),
      notes: map['notes'] as String?,
      status: map['status'] as String,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderDate': orderDate.toIso8601String(),
      'totalEstimatedCost': totalEstimatedCost,
      'notes': notes,
      'status': status,
    };
  }

  // Copy with method for updates
  OrderModel copyWith({
    int? id,
    DateTime? orderDate,
    double? totalEstimatedCost,
    String? notes,
    String? status,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderDate: orderDate ?? this.orderDate,
      totalEstimatedCost: totalEstimatedCost ?? this.totalEstimatedCost,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'OrderModel(id: $id, orderDate: $orderDate, '
        'totalEstimatedCost: $totalEstimatedCost, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel &&
        other.id == id &&
        other.orderDate == orderDate &&
        other.totalEstimatedCost == totalEstimatedCost &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderDate.hashCode ^
        totalEstimatedCost.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
