import '../entities/activity.dart';
import '../repositories/activity_repository.dart';

class GetRecentActivitiesUseCase {
  final ActivityRepository _repository;

  GetRecentActivitiesUseCase(this._repository);

  Future<List<Activity>> call({
    int limit = 10,
    int offset = 0,
    bool recentOnly = false,
  }) async {
    try {
      if (recentOnly) {
        return await _repository.getRecentActivities(limit: limit);
      } else {
        return await _repository.getActivities(limit: limit, offset: offset);
      }
    } catch (e) {
      throw Exception('Failed to get recent activities: $e');
    }
  }

  Future<Map<String, dynamic>> getStatistics() async {
    try {
      return await _repository.getActivityStatistics();
    } catch (e) {
      throw Exception('Failed to get activity statistics: $e');
    }
  }

  Future<int> getActivitiesCount() async {
    try {
      return await _repository.getActivitiesCount();
    } catch (e) {
      throw Exception('Failed to get activities count: $e');
    }
  }
}
