import '../entities/order.dart';
import '../entities/order_item.dart';

abstract class OrderRepository {
  /// Create a new order with its items
  Future<int> createOrder(Order order, List<OrderItem> items);

  /// Get all orders
  Future<List<Order>> getAllOrders();

  /// Get order by ID
  Future<Order?> getOrderById(int id);

  /// Get order items by order ID
  Future<List<OrderItem>> getOrderItems(int orderId);

  /// Update order
  Future<void> updateOrder(Order order);

  /// Update order with items
  Future<void> updateOrderWithItems(Order order, List<OrderItem> items);

  /// Delete order
  Future<void> deleteOrder(int id);

  /// Get orders by status
  Future<List<Order>> getOrdersByStatus(String status);

  /// Get order statistics
  Future<Map<String, dynamic>> getOrderStatistics();
}
